/**
 * Copyright (c) 2013-Now http://aidex.vip All rights reserved.
 */

// 判断是否为开发环境
export const isDevelopment = process.env.NODE_ENV === 'development'

const config = {
	
	// 产品名称
	productName: '浩拓技术',
	
	// 公司名称
	companyName: '浩拓科技',
	
	// 产品版本号
	productVersion: 'V4.3.0',
	
	// 版本检查标识
	appCode: 'android',
	
	// 内部版本号码
	appVersion: 1,
	
	// 管理基础路径
	adminPath: '',
	
	// 环境判断
	isDevelopment: isDevelopment,
	
	// 生产环境API URL
	productionApiUrl: 'https://hcscmpre.zzfzyc.com/hcscm/pda/v1',
	
	// 开发环境API URL选项
	devApiUrlOptions: [
		{ label: '测试环境', value: 'https://hcscmtest.zzfzyc.com/hcscm/pda/v1' },
		{ label: '预发布环境', value: 'https://hcscmpre.zzfzyc.com/hcscm/pda/v1' },
		{ label: '自定义', value: 'custom' }
	]
	
}

// 设置后台接口服务的基础地址 - 使用动态API URL
// config.baseUrl = 'https://aidex.vip/api'; // 注释掉固定的baseUrl

export default config;
