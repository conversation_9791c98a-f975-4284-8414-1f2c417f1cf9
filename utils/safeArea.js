const SYSTEM_INFO = uni.getSystemInfoSync();

/**
 * 获取状态栏高度
 */
export const getStatusBarHeight = () => {
    // APP和小程序都可以直接获取状态栏高度
    return SYSTEM_INFO.statusBarHeight || 0;
};

/**
 * 获取导航栏高度
 */
export const getNavigationBarHeight = () => {
    // #ifdef MP-WEIXIN
    // 微信小程序，通过胶囊按钮计算
    const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
    if (menuButtonInfo && menuButtonInfo.top && menuButtonInfo.height) {
        const statusBarHeight = getStatusBarHeight();
        return (menuButtonInfo.top - statusBarHeight) * 2 + menuButtonInfo.height;
    }
    // #endif

    // #ifdef APP-PLUS
    // APP端可以自定义导航栏高度，一般是44px
    return 44;
    // #endif

    // #ifdef MP-ALIPAY || MP-BAIDU || MP-TOUTIAO
    // 其他小程序平台使用固定高度
    return 45;
    // #endif

    return 44; // 默认值
};

/**
 * 获取完整的顶部安全区高度
 */
export const getTopSafeAreaHeight = () => {
    const statusBarHeight = getStatusBarHeight();
    
    // #ifdef APP-PLUS
    // APP一般只需要状态栏高度，因为导航栏是自定义的
    return statusBarHeight;
    // #endif
    
    // #ifdef MP
    // 小程序需要状态栏+导航栏的完整高度
    return statusBarHeight + getNavigationBarHeight();
    // #endif

    return statusBarHeight; // 默认值
}; 