<template>
	<view class="myCard" @click="gotoDetailFun">
		<view class="cardTopName">盘点单号：{{item.order_no}}</view>
		<view class="cardRow">
			<view>盘点日期：</view>
			<view>{{item.check_time}}</view>
		</view>
		<view class="cardRow">
			<view>仓库名称：</view>
			<view>{{item.warehouse_name}}</view>
		</view>
		<view class="cardRow">
			<view>仓位名称：</view>
			<view>{{item.warehouse_bin_name}}</view>
		</view>
		<view class="cardRow">
			<view>创 建 人：</view>
			<view>{{item.create_user_name}} {{item.create_time}}</view>
		</view>
		<view class="cardRow">
			<view>审 核 人：</view>
			<view>{{item.update_user_name}} {{item.update_time}}</view>
		</view>
		<view v-if="!isDetail && !isSelect" class="lookDetail">
			<text>查看详情</text>
			<u-icon name="arrow-right" size="36"></u-icon>
		</view>
	</view>
</template>

<script>	
	export default {
		props: {
			item: {
				type: Object,
				default: () => {}
			},
			index: {
				type: Number,
				default: 0
			},
			isSelect: {
				type: Boolean,
				default: false
			},
			isDetail: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				
			}
		},
		methods: {
			onlaunch(e) {
				console.log("---->>>" + item.OpDate);
			},
				
			gotoDetailFun: function() {
				if(this.isSelect) {
					return
				}
				uni.$bjInfo = this.item;
				uni.navigateTo({
					url: '/pages/saleship/detail?index=' + this.index
				})
			}
		}
	}
</script>

<style>
	
</style>

