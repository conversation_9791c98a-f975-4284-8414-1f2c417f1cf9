<template>
	<view class="myCard" @click="gotoDetailFun">
		<view class="cardTopName">排产单号：{{item.ProductWeaveNo}} {{item.WeaveFactoryName}}</view>
		<view class="cardRow">
			<view>排产日期：</view>
			<view>{{item.ProductWeaveDate}}</view>
		</view>
		<view class="cardRow">
			<view>营销部门：</view>
			<view>{{item.PlanDepartmentName}}</view>
		</view>
		<view class="cardRow">
			<view>客户名称：</view>
			<view>{{item.CustomerName}}</view>
		</view>
		<view class="cardRow">
			<view>销 售 员：</view>
			<view>{{item.SaleUserName}}</view>
		</view>
		<view class="cardRow">
			<text class="fzrLeft">单据状态：</text>
			<text class="fzrRight">{{item.BillStatusName}} {{item.BillStatusTime}}</text>
		</view>
		<view class="cardRow">
			<view>物流公司：</view>
			<view>{{item.ShipCompanyName}}</view>
		</view>
		<view class="cardRow">
			<view>发货地址：</view>
			<view>{{item.SaleCustomerAddress}}</view>
		</view>
		<view class="cardRow">
			<view>创 建 人：</view>
			<view>{{item.OpName}} {{item.OpDate}}</view>
		</view>
		<view class="cardRow">
			<view>审 核 人：</view>
			<view>{{item.CommitName}} {{item.CommitDate}}</view>
		</view>
		<view v-if="!isDetail && !isSelect" class="lookDetail">
			<text>查看详情</text>
			<u-icon name="arrow-right" size="36"></u-icon>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			item: {
				type: Object,
				default: () => {}
			},
			index: {
				type: Number,
				default: 0
			},
			isSelect: {
				type: Boolean,
				default: false
			},
			isDetail: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				
			}
		},
		methods: {
			gotoDetailFun: function() {
				if(this.isSelect) {
					return
				}
				uni.$bjInfo = this.item;
				uni.navigateTo({
					url: '/pages/saleship/detail?index=' + this.index
				})
			}
		}
	}
</script>

<style>
	
</style>

