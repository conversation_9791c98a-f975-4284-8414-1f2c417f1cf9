<template>
	<view class="wrap">
		<!--u-form :model="model" :rules="rules" ref="uForm" :errorType="errorType"-->
			<u-form :model="model"  ref="uForm">
			<u-form-item label-width="150" label="扫描仓位:">
				<input type="text" v-model="model.stationStationNo" :focus="testFocus0" style="width:100px;" @confirm="GetStoreStationName" />
				<text class="title" style="width:80px;">名称：{{model.stationStationName}}</text>
				<!--u-icon slot="right" name="scan" @tap="scanStoreStationNo"></u-icon-->
			</u-form-item>
			<u-form-item label-width="150" label="原料名称:">
				<u-input v-model="model.YarnGoodsName" type="text" />
			</u-form-item>
			<u-form-item label-width="150" label="色号颜色:">
				<u-input v-model="model.YarnGoodsCodeNoName" type="text" />
			</u-form-item>
			<u-form-item label-width="150" label="缸号资料:">
				<u-input v-model="model.YarnCrockNo" type="text" />
			</u-form-item>
			<u-form-item label-width="150" label="每包个数:">
				<u-input v-model="model.YarnGrossQty" type="text" />
			</u-form-item>
			<u-form-item label-width="150" label="原料毛重:">
				<u-input v-model="model.YarnGrossQty" type="text" />
			</u-form-item>
			<u-form-item label-width="150" label="原料净重:">
				<u-input v-model="model.YarnNetQty" type="text" />
			</u-form-item>
		</u-form>
		<u-row gutter="32" class="bottom-box" justify="center">
			<u-col span="10">
				<view><u-button type="primary" shape="circle" @click="navTo('/pages/sys/home/<USER>')">提交</u-button></view>
			</u-col>
		</u-row>
	</view>
</template>

<script>
	import util, {
		parFabricGoodsBarCode2D,
		playAppRightAudio,
		playAppErrorAudio
	} from '../../common/util';
	export default {
		components: {
		},
		data() {
			return {
				testFocus0: true, // TODO: 修改正确的变量名， 暂时未测试名字
				testFocus1: false,
				model: {
					stationStationNo:'',
					stationStationName: '',
					YarnGoodsNo:'',
					YarnGoodsName:'',
					YarnGoodsCodeNo:'',
					YarnGoodsCodeName:'',
					YarnGoodsCodeNoName:'',
					YarnCrockNo:'',
					YarnBoxBillNo:'',
					yarnBoxRoll:'',  //每箱个数
					YarnRoll:'',
					YarnGrossQty:'',
					YarnNetQty:'',
				}
			};
		},
		onLoad(option) {

		},
		onUnload() {

		},
		methods: {
			playSuccess() {
				util.playSuccessAudio();
			},
			playError() {
				util.playErrorAudio();
			},

			GetStoreStationName: function() {
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetStoreStationName',
							params: [{
								name: 'No',
								value: this.model.stationStationNo,
							}]
						},
					},
					success: (res) => {
						console.log("---->" + res.data.toString());
						if (res.data.status == 0 && res.data.count > 0) {
							this.playSuccess();
							var aResultData = res.data.data;
							for (var i = 0; i < aResultData.length; i++) {
								this.StoreStationName = aResultData[i].StoreStationName;
								this.StoreNameID = parseInt(aResultData[i].StoreNameID);
							};
							if (this.DyeBackCheckStatus)
							{
								this.testFocus0 = false;
								this.$nextTick(() => {
									this.testFocus1 = false;
									this.testFocus0 = false;
									this.testFocus2 = true;
								});
							}
							else{
								this.testFocus0 = false;
								this.$nextTick(() => {
									this.testFocus1 = true;
									this.testFocus0 = false;
									this.testFocus2 = false;
								});
							}

						} else {
							this.playError();
							this.StoreStationNo = "";
							this.StoreStationName = "",
								this.StoreNameID = 0,
								this.testFocus0 = false;
							this.$nextTick(() => {
								this.testFocus0 = true;
								this.testFocus1 = false;
								this.testFocus2 = false;
							});
							this.BillDataMessage = '不存在当前的仓位资料，请确认！';
						}
					},
					fail: (error) => {
						this.playError();
						this.BillDataMessage = '连接服务器出错，请检查后台服务是否启动！' + res.data.msg;
					},
				})
			},
			confirm(e) {
				if (e.length > 0) {
					this.model.stationStationNo = e[0].value;
				}
			},
			scanStoreStationNo() {
				uni.scanCode({
					scanType: ['barCode', 'qrCode'],
					success: res => {
						this.model.stationStationNo = res.result;
						this.model.stationStationName = res.result;
					}
				});
			},
		}
	};
</script>

<style scoped lang="scss">
.wrap {
	padding: 30rpx;
}

.agreement {
	display: flex;
	align-items: center;
	margin: 40rpx 0;

	.agreement-text {
		padding-left: 8rpx;
		color: $u-tips-color;
	}
}
</style>
