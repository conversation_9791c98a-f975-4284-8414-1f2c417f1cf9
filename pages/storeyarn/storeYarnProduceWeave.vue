<template>
	<view class="wrap">
		<u-form ref="uForm">
			<u-form-item label-width="150" label="配纱单号:">
				<input type="text" v-model="OutStoreBillNo" :focus="testFocus0" style="width:100px;" @confirm="GetDetailListData" />
				<button class="mini-btn" type="primary" size="mini" @tap="CreateStoreYarnOutBill">生成出仓</button>
				<button class="mini-btn" type="primary" size="mini" @tap="ClearBillData">清空</button>
			</u-form-item>
			<u-form-item label-width="150" label="原料名称:">
				<text class="title" style="width:150px;">{{YarnGoodsName}}</text>
				<text class="title" style="width:150px;">缸号：{{YarnCrockNo}}</text>
			</u-form-item>
			<u-form-item label-width="150" label="色号颜色:">
				<text class="title" style="width:300px;">{{YarnGoodsCodeNo}} {{YarnGoodsCodeName}} {{YarnGoodsColorNo}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:80px;">箱号：{{YarnBoxBillNo}}</text>
				<text class="title" style="width:90px;">个数：{{YarnBoxRoll}}</text>
				<text class="title" style="width:90px;">毛重：{{YarnGrossQty}}</text>
				<text class="title" style="width:90px;">净重：{{YarnNetQty}}</text>
			</u-form-item>
			<u-form-item label-width="150" label="条码资料:">
				<input type="text" v-model="QRBarCode" :focus="testFocus1" style="width:250px;"
					@confirm="CreateNewBillDetail" />
			</u-form-item>
			<u-form-item>
				<text class="title">{{BillDataMessage}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">需求件数：{{YarnPlanRoll}}</text>
				<text class="title" style="width:200px;">需求重量：{{YarnPlanQty}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">已扫件数：{{YarnScanRoll}}</text>
				<text class="title" style="width:200px;">已扫重量：{{YarnScanQty}}</text>
			</u-form-item>
		</u-form>

		<view class="u-demo-area">
			<u-toast ref="uToast"></u-toast>
			<wyb-table ref="table" :headers="headersMaster" :contents="YarnGoodsDetailDataList"/>
		</view>
	</view>
</template>
<script>
	import util, {
		parFabricGoodsBarCode2D,
		parYarnGoodsBarCode2D,
		playSuccessAudio,
		playErrorAudio
	} from '../../common/util';
	import wybTable from '@/components/wyb-table/wyb-table.vue';
	export default {
		data() {
			return {
				borderColor: '#e4e7ed',
				align: 'center',
				index: 0,
				YarnDetailList: [],
				QRBarCode: '',
				OutStoreBillID: 0,
				OutStoreBillNo: '',
				OutStoreMasterNo: '',
				StoreNameID: 0,
				StoreStationNo: '',
				StoreStationName: '',
				DyeWorksDigCode: 0,
				YarnGoodsNo: '',
				YarnGoodsName: '',
				YarnGoodsCodeNo: '',
				YarnGoodsCodeName: '',
				YarnGoodsColorNo: '',
				YarnCrockNo: '',
				YarnBoxBillNo: '',
				YarnBoxRoll: '', //每箱个数
				YarnRoll: '',
				YarnGrossQty: '',
				YarnNetQty: '',
				YarnPlanRoll: 0,
				YarnPlanQty: 0,
				YarnScanRoll: 0,
				YarnScanQty: 0,
				testFocus0: true, // TODO: 修改正确的变量名， 暂时未测试名字
				testFocus1: false,
				testFocus2: false,
				BillDataMessage: '',
				YarnGoodsDetailDataList:[],
				headersMaster: [{
				                label: '名称',
				                key: 'YarnName'
				            }, {
				                label: '色号',
				                key: 'YarnColorNo'
				            },{
				                label: '颜色',
				                key: 'YarnColorName'
				            },{
				                label: '缸号',
				                key: 'YarnCrockNo'
				            }, {
				                label: '扫描件数',
				                key: 'YarnScanRoll'
				            }, {
				                label: '扫描重量',
				                key: 'YarnScanQty'
				            },{
				                label: '件数',
				                key: 'YarnPlanRoll'
				            }, {
				                label: '重量',
				                key: 'YarnPlanQty'
				            }],
			};
		},
		methods: {
			playSuccess() {
				util.playSuccessAudio();
			},
			playError() {
				util.playErrorAudio();
			},
			borderColorChange(index) {
				this.borderColor = index == 0 ? '#e4e7ed' : index == 1 ? '#2979ff' : '#ff9900';
			},
			alignChange(index) {
				this.align = index == 0 ? 'left' : index == 1 ? 'center' : 'right';
			},

			ClearBillData: function() {
				this.testFocus0 = false;
				this.$nextTick(() => {
					this.testFocus0 = true;
				});
				this.YarnGoodsDetailDataList = [],
				this.QRBarCode = '',
				this.OutStoreBillID = 0,
				this.OutStoreBillNo = '',
				this.OutStoreMasterNo = '',
				this.StoreNameID = 0,
				this.StoreStationNo = '',
				this.StoreStationName = '',
				this.DyeWorksDigCode = 0,
				this.YarnGoodsNo = '',
				this.YarnGoodsName = '',
				this.YarnGoodsCodeNo = '',
				this.YarnGoodsCodeName = '',
				this.YarnGoodsColorNo = '',
				this.YarnCrockNo = '',
				this.YarnBoxBillNo = '',
				this.YarnBoxRoll = '',
				this.YarnRoll = '',
				this.YarnGrossQty = '',
				this.YarnNetQty = '',
				this.YarnPlanRoll = 0,
				this.YarnPlanQty = 0,
				this.YarnScanRoll = 0,
				this.YarnScanQty = 0
			},

			CreateStoreYarnOutBill() {
				this.testFocus1 = false;
				this.$nextTick(() => {
					this.testFocus0 = false;
					this.testFocus1 = true;
					this.testFocus2 = false;
				});

				if (this.OutStoreBillID == 0) {
					this.playError();
					this.BillDataMessage = '配纱单号不能为空';
					return;
				}
				uni.request({
					url: util.apiurl + 'rest/db/storedproc',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							proc_name: 'Usp_APP_ProductPlanYarnOutCreateBill',
							method: 'open_proc',
							params: [{
									name: '@BillID',
									value: this.OutStoreBillID
								},
								{
									name: '@BillNo',
									value: this.OutStoreBillNo
								},
								{
									name: '@UserName',
									value: getApp().globalData.UserName
								}
							]
						},
					},
					success: (res) => {
						if (res.data.status == 0) {
							var aResultData = JSON.parse(res.data.data);
							if (aResultData.BillDataStatus == '0' && aResultData.BillDataMessage ==
								'SUCCESS') {
								this.playSuccess();
								this.testFocus1 = false;
								this.$nextTick(() => {
									this.testFocus0 = false;
									this.testFocus1 = false;
									this.testFocus2 = true;
								});
							} else {
								this.playError();
								this.BillDataMessage = '生成出仓单出错！' + aResultData.BillDataMessage;
								return;
							}
						} else {
							this.playError();
							this.BillDataMessage = '生成出仓单出错，' + res.data.msg
							return;
						}
					},
					fail: (error) => {
						this.playError();
						this.BillDataMessage = '连接服务器出错，请检查后台服务是否启动！' + res.data.msg;
					},
				})
			},

			GetDetailListData: function() {
				console.log("-----<<<<<<>>>>");
				this.CarryDetailList = [];
				this.BillID = 0;
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP_GetStoreYarnPlanOutDetailSQL',
							params: [{
								name: 'BillNo',
								value: this.OutStoreBillNo
							}]
						},
					},
					success: (res) => {
						console.log("--->>>" + JSON.stringify(res.data));
						if (res.data.status == 0 && res.data.count > 0) {
							this.playSuccess();
							var aResultData = res.data.data;
							this.YarnPlanRoll = 0;
							this.YarnPlanQty = 0;
							this.YarnScanRoll = 0;
							this.YarnScanQty = 0;
							this.YarnGoodsDetailDataList = [];
							for (var i = 0; i < aResultData.length; i++) {
								this.YarnGoodsDetailDataList.push(aResultData[i]);
								this.OutStoreBillID = aResultData[i].BillMasterID;
								this.YarnPlanRoll = this.YarnPlanRoll + aResultData[i].YarnPlanRoll;
								this.YarnPlanQty = this.YarnPlanQty + aResultData[i].YarnPlanQty;
								this.YarnScanRoll = this.YarnScanRoll + aResultData[i].YarnScanRoll;
								this.YarnScanQty = this.YarnScanQty + aResultData[i].YarnScanQty;
							};
							console.log("--->" + this.YarnScanRoll + "<>"  + this.YarnScanQty);
							console.log("--->" + this.OutStoreBillID + "<>"  + this.YarnPlanRoll);
							this.testFocus1 = false;
							this.$nextTick(() => {
								this.testFocus1 = true;
							});

						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.playError();
							this.testFocus1 = false;
							this.$nextTick(() => {
								this.testFocus1 = true;
							});
						} else {
							this.playError();
							this.testFocus1 = false;
							this.$nextTick(() => {
								this.testFocus1 = true;
							});
						}
					},
					fail: (error) => {
						this.playError();
						this.testFocus1 = false;
						this.$nextTick(() => {
							this.testFocus1 = true;
						});
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},

			CreateNewBillDetail() {
				this.testFocus2 = false;
				this.$nextTick(() => {
					this.testFocus0 = false;
					this.testFocus1 = false;
					this.testFocus2 = true;
				});

				if (this.StoreNameID == 0 && this.InStoreMasterNo == '') {
					this.playError();
					this.BillDataMessage = '请先新增单据';
					return;
				}

				var aStrBarCode = parYarnGoodsBarCode2D(this.QRBarCode);
				aStrBarCode = aStrBarCode.trimRight().trimLeft();
				if (aStrBarCode == '') {
					this.playError();
					this.QRBarCode = '';
					this.testFocus2 = true;
					this.BillDataMessage = '无效的二维码！';
					return;
				}
				console.log("-------->" + this.OutStoreBillID + "<----");
				var aBarCodeList = aStrBarCode.split(',');
				this.DyeWorksDigCode = aBarCodeList[0];
				this.YarnGoodsCodeNo = aBarCodeList[2];
				this.YarnGoodsColorNo = aBarCodeList[3];
				this.YarnCrockNo = aBarCodeList[5];
				this.YarnBoxBillNo = aBarCodeList[6];
				this.YarnBoxRoll = aBarCodeList[7];
				this.YarnGrossQty = aBarCodeList[8];
				this.YarnNetQty = aBarCodeList[9];
				uni.request({
					url: util.apiurl + 'rest/db/storedproc',
					data: {
						token: getApp().globalData.Token,
						data: {
							db_name: getApp().globalData.AppDBName,
							proc_name: 'Usp_APP_ProductPlanYarnOutScan',
							method: 'open_proc',
							params: [{
									name: '@BillID',
									value: this.OutStoreBillID
								},
								{
									name: '@BillTypeID',
									value: '0'
								},
								{
									name: '@StoreStationName',
									value: ''
								},
								{
									name: '@QRBarCode',
									value: aStrBarCode
								},
								{
									name: '@UserName',
									value: getApp().globalData.UserName
								}
							]
						},
					},
					success: (res) => {
						if (res.data.status == 0) {
							console.log("--->>>" + res.data.data.toString());
							var aResultData = JSON.parse(res.data.data);
							if (aResultData.BillDataStatus == '0' && aResultData.BillDataMessage == 'SUCCESS') {
								this.playSuccess();
								this.YarnGoodsName = aResultData.YarnGoodsName;
								this.YarnGoodsCodeName = aResultData.YarnGoodsCodeName;
								this.YarnPlanRoll = aResultData.YarnPlanRoll;
								this.YarnPlanQty = aResultData.YarnPlanQty;
								this.YarnScanRoll = aResultData.YarnScanRoll;
								this.YarnScanQty = aResultData.YarnScanQty;
								this.QRBarCode = '';
								this.BillDataMessage = '扫描成功！';
								this.GetDetailListData();
								this.testFocus2 = false;
								this.$nextTick(() => {
									this.testFocus0 = false;
									this.testFocus1 = false;
									this.testFocus2 = true;
								});
							} else {
								this.playError();
								this.BillDataMessage = '扫描出仓出错，' + aResultData.BillDataMessage;
								this.QRBarCode = '';
								this.testFocus2 = false;
								this.$nextTick(() => {
									this.testFocus0 = false;
									this.testFocus1 = false;
									this.testFocus2 = true;
								});
								return;
							}

						} else {
							this.playError();
							this.QRBarCode = '';
							this.BillDataMessage = '扫描出仓出错，' + res.data.msg;
							this.testFocus2 = false;
							this.$nextTick(() => {
								this.testFocus0 = false;
								this.testFocus1 = false;
								this.testFocus2 = true;
							});
							return;
						}
					},
					fail: (error) => {
						this.playError();
						this.QRBarCode = '';
						this.testFocus2 = false;
						this.$nextTick(() => {
							this.testFocus0 = false;
							this.testFocus1 = false;
							this.testFocus2 = true;
						});
						this.BillDataMessage = '扫描出仓出错，' + res.data.msg;
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			}
		},
	}
</script>
<style>
	button {
		margin-top: 30upx;
		margin-bottom: 30upx;
	}

	.button-sp-area {
		margin: 0 auto;
		width: 60%;
	}

	.mini-btn {
		margin-right: 10upx;
	}
</style>
