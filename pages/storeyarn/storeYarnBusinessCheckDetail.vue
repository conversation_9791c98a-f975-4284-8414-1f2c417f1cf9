<template>
	<view class="wrap">
		<u-form ref="uForm">
			<u-form-item>
				<text class="title" style="width:200px;">单号：{{BillNo}}</text>
				<text class="title" style="width:200px;">日期：{{BillDate}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">往来单位：{{CustomerName}}</text>
				<text class="title" style="width:200px;">仓库名称：{{StoreName}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:80px;">仓位编号：</text>
				<input type="text" v-model="StoreStationNo" :focus="testFocus0" style="width:120px;" @confirm="GetStoreStationName" />
				<text class="title" style="width:120px;">仓库名称：{{StoreStationName}}</text>
			</u-form-item>
			<u-form-item label-width="150" label="条码资料:">
				<input type="text" v-model="QRBarCode" :focus="testFocus1" style="width:250px;"
					@confirm="CheckBillDetailScan" />
			</u-form-item>
			<u-form-item>
				<text class="title">{{BillDataMessage}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">原料名称：{{YarnName}}</text>
				<text class="title" style="width:200px;">原料缸号：{{YarnCrockNo}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">原料色号：{{YarnColorNo}}</text>
				<text class="title" style="width:200px;">原料颜色：{{YarnColorName}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">本缸盘前：{{YarnCrockNoOldRoll}}件</text>
				<text class="title" style="width:200px;">本缸盘前：{{YarnCrockNoOldQty}}Kg</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">本缸实盘：{{YarnCrockNoNewRoll}}件</text>
				<text class="title" style="width:200px;">本缸实盘：{{YarnCrockNoNewQty}}Kg</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">本缸盈亏：{{YarnCrockNoSumRoll}}件</text>
				<text class="title" style="width:200px;">本缸盈亏：{{YarnCrockNoSumQty}}Kg</text>
			</u-form-item>

			<u-form-item>
				<text class="title" style="width:200px;">本单盘前：{{YarnOldRoll}}件</text>
				<text class="title" style="width:200px;">本单盘前：{{YarnOldQty}}Kg</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">本单实盘：{{YarnNewRoll}}件</text>
				<text class="title" style="width:200px;">本单实盘：{{YarnNewQty}}Kg</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">本单盈亏：{{YarnSumRoll}}件</text>
				<text class="title" style="width:200px;">本单盈亏：{{YarnSumQty}}Kg</text>
			</u-form-item>
		</u-form>
		<view class="u-demo-area">
			<u-toast ref="uToast"></u-toast>
			<wyb-table ref="table" :headers="headersMaster" :contents="CheckDetailList"/>
			<!--	<wyb-table ref="table" :headers="headersDetail" :contents="GoodsCrockNoDataList"  height="600rpx" /> !-->
		</view>
		<!-- 		<u-action-sheet :list="InDetailList" v-model="actionSheetShow"  @click="actionSheetCallback"></u-action-sheet> -->
	</view>
</template>

<script>
	import util, {
		parFabricGoodsBarCode2D,
		parYarnGoodsBarCode2D,
		playSuccessAudio,
		playErrorAudio
	} from '../../common/util';
	import wybTable from '@/components/wyb-table/wyb-table.vue';
	let that = '';
	export default {
		data() {
			return {
				BillMasterID: 0,
				borderColor: '#e4e7ed',
				align: 'center',
				index: 0,
				actionSheetShow: false,
				QRBarCode: '',
				BillNo: '',
				BillDate: '',
				CustomerName: '',
				StoreNameID: 0,
				StoreName: '',
				StoreStationNo: '',
				StoreStationName: '',
				YarnName: '',
				YarnColorNo: '',
				YarnColorName: '',
				YarnCrockNo: '',
				testFocus0: true,
				YarnCrockNoOldRoll: 0,
				YarnCrockNoOldQty: 0,
				YarnCrockNoNewRoll: 0,
				YarnCrockNoNewQty: 0,
				YarnCrockNoSumRoll: 0,
				YarnCrockNoSumQty: 0,
				YarnOldRoll: 0,
				YarnOldQty: 0,
				YarnNewRoll: 0,
				YarnNewQty: 0,
				YarnSumRoll: 0,
				YarnSumQty: 0,
				CheckDetailList: [],
				BillDataMessage: '',
				headersMaster: [{
					label: '原料名称',
					key: 'YarnName'
				}, {
					label: '色号',
					key: 'YarnColorNo'
				}, {
					label: '颜色',
					key: 'YarnColorName'
				}, {
					label: '原料缸号',
					key: 'YarnCrockNo'
				}, {
					label: '盘前件数',
					key: 'OldRoll'
				}, {
					label: '盘前重量',
					key: 'OldQty'
				}, {
					label: '实盘件数',
					key: 'NewRoll'
				}, {
					label: '实盘重量',
					key: 'NewQty'
				},{
					label: '盈亏件数',
					key: 'Roll'
				}, {
					label: '盈亏重量',
					key: 'Qty'
				}],
			}
		},

		onLoad(e) {
			that = this;
			if (e.billid) {
				that.BillMasterID = e.billid;
			}
			console.log("---BillID->" + this.BillMasterID);
			this.StoreYarnBusinessCheckDetail();
		},

		methods: {
			playSuccess() {
				util.playSuccessAudio();
			},
			playError() {
				util.playErrorAudio();
			},

			GetStoreStationName: function() {
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetStoreStationName',
							params: [{
								name: 'No',
								value: this.StoreStationNo
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							this.playSuccess();
							var aResultData = res.data.data;
							for (var i = 0; i < aResultData.length; i++) {
								this.StoreStationName = aResultData[i].StoreStationName;
							};
							this.testFocus0 = false;
							this.$nextTick(() => {
								this.testFocus1 = true;
								this.testFocus0 = false;
							});
						} else {
							this.playError();
							this.StoreStationName = "",
							this.testFocus0 = false;
							this.$nextTick(() => {
								this.testFocus1 = false;
								this.testFocus0 = true;
							});
							this.StoreNameID = 0,
							this.BillDataMessage = '不存在当前的仓位资料，请确认！';
						}
					},
					fail: (error) => {
						this.playError();
						this.BillDataMessage = '连接服务器出错，请检查后台服务是否启动！' + res.data.msg;
					},
				})
			},

			CheckBillDetailScan() {
				this.testFocus0 = false;
				this.$nextTick(() => {
					this.testFocus0 = true;
				});

				var aStrBarCode = parYarnGoodsBarCode2D(this.QRBarCode);
				aStrBarCode = aStrBarCode.trimRight().trimLeft();
				if (aStrBarCode == '') {
					this.playError();
					this.QRBarCode = '';
					this.testFocus2 = true;
					this.BillDataMessage = '无效的二维码！';
					return;
				}
				console.log("-------->" + aStrBarCode + "<----");
				var aBarCodeList = aStrBarCode.split(',');
				this.DyeWorksDigCode = aBarCodeList[0];
				this.YarnGoodsCodeNo = aBarCodeList[2];
				this.YarnGoodsColorNo = aBarCodeList[3];
				this.YarnCrockNo = aBarCodeList[5];
				this.YarnBoxBillNo = aBarCodeList[6];
				this.YarnBoxRoll = aBarCodeList[7];
				this.YarnGrossQty = aBarCodeList[8];
				this.YarnNetQty = aBarCodeList[9];
				uni.request({
					url: util.apiurl + 'rest/db/storedproc',
					data: {
						token: getApp().globalData.Token,
						data: {
							db_name: getApp().globalData.AppDBName,
							proc_name: 'Usp_APP_StoreYarnCreateCheckBillDetail',
							method: 'open_proc',
							params: [{
									name: '@BillID',
									value: this.BillMasterID
								},
								{
									name: '@StoreNameID',
									value: this.StoreNameID
								},
								{
									name: '@StoreStationNo',
									value: this.StoreStationNo
								},
								{
									name: '@StoreStationName',
									value: this.StoreStationName
								},
								{
									name: '@QRBarCode',
									value: aStrBarCode
								},
								{
									name: '@UserName',
									value: getApp().globalData.UserName
								}
							]
						},
					},
					success: (res) => {
						if (res.data.status == 0) {
							console.log(res.data.data.toString());
							var aResultData = JSON.parse(res.data.data);
							if (aResultData.BillDataStatus == '0' && aResultData.BillDataMessage ==
								'SUCCESS') {
								this.playSuccess();
								this.YarnName = aResultData.YarnName;
								this.YarnColorNo = aResultData.YarnColorNo;
								this.YarnColorName = aResultData.YarnColorName;
								this.YarnCrockNo = aResultData.YarnCrockNo
								this.YarnCrockNoOldRoll = parseFloat(aResultData.YarnCrockNoOldRoll);
								this.YarnCrockNoOldQty = parseFloat(aResultData.YarnCrockNoOldQty);
								this.YarnCrockNoNewRoll = parseFloat(aResultData.YarnCrockNoNewRoll);
								this.YarnCrockNoNewQty = parseFloat(aResultData.YarnCrockNoNewQty);
								this.YarnCrockNoSumRoll = parseFloat(aResultData.YarnCrockNoSumRoll);
								this.YarnCrockNoSumQty = parseFloat(aResultData.YarnCrockNoSumQty);
								this.YarnOldRoll = parseFloat(aResultData.YarnOldRoll);
								this.YarnOldQty = parseFloat(aResultData.YarnOldQty);
								this.YarnNewRoll = parseFloat(aResultData.YarnNewRoll);
								this.YarnNewQty = parseFloat(aResultData.YarnNewQty);
								this.YarnSumRoll = parseFloat(aResultData.YarnSumRoll);
								this.YarnSumQty = parseFloat(aResultData.YarnSumQty);
								this.QRBarCode = '';
								this.BillDataMessage = '扫描成功！';
								this.testFocus1 = false;
								this.$nextTick(() => {
									this.testFocus0 = false;
									this.testFocus1 = true;
								});
								this.StoreYarnBusinessCheckDetail();
							} else {
								this.playError();
								this.BillDataMessage = '扫描出仓出错，' + aResultData.BillDataMessage;
								this.QRBarCode = '';
								this.testFocus1 = false;
								this.$nextTick(() => {
									this.testFocus0 = false;
									this.testFocus1 = true;
								});
								return;
							}

						} else {
							this.playError();
							this.QRBarCode = '';
							this.BillDataMessage = '扫描出仓出错，' + res.data.msg;
							this.testFocus1 = false;
							this.$nextTick(() => {
								this.testFocus0 = false;
								this.testFocus1 = true;
							});
							return;
						}
					},
					fail: (error) => {
						this.playError();
						this.QRBarCode = '';
						this.testFocus1 = false;
						this.$nextTick(() => {
							this.testFocus0 = false;
							this.testFocus1 = true;
						});
						this.BillDataMessage = '入仓出错，' + res.data.msg;
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},

			StoreYarnBusinessCheckDetail: function() {
				if (this.BillMasterID == 0) {
					this.BillMasterID = 30685
				}
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetStoreYarnBusinessCheckDetailSQL',
							params: [{
								name: 'BillID',
								value: this.BillMasterID
							}]
						},
					},
					success: (res) => {
						this.triggered = false;
						let data = res.data.data;
						console.log("---->" + JSON.stringify(res.data));
						if (res.data.status == 0 && res.data.count > 0) {
							var aResultData = res.data.data;
							this.CheckDetailList = res.data.data;
							this.BillNo = aResultData[0].BillNo;
							this.BillDate = aResultData[0].BillDate;
							this.CustomerName = aResultData[0].CustomerName;
							this.StoreNameID = aResultData[0].StoreNameID;
							this.StoreName = aResultData[0].StoreName;
							this.StoreStationNo = aResultData[0].ScanStoreStationNo;
							this.StoreStationName = aResultData[0].ScanStoreStationName;
							//this.YarnCrockNoOldRoll = 0;
							//this.YarnCrockNoOldQty = 0;
							//this.YarnCrockNoNewRoll = 0;
							//this.YarnCrockNoNewQty = 0;
							//this.YarnCrockNoSumRoll = 0;
							//this.YarnCrockNoSumQty = 0;
							this.YarnOldRoll = 0;
							this.YarnOldQty = 0;
							this.YarnNewRoll = 0;
							this.YarnNewQty = 0;
							this.YarnSumRoll = 0;
							this.YarnSumQty = 0;
							for (var i = 0; i < aResultData.length; i++) {
								if (parseFloat(aResultData[i].OldRoll) != 0) {
									this.YarnOldRoll = this.YarnOldRoll + aResultData[i].OldRoll;
								}
								if (parseFloat(aResultData[i].OldQty) != 0) {
									this.YarnOldQty = this.YarnOldQty + aResultData[i].OldQty;
								}
								if (parseFloat(aResultData[i].NewRoll) != 0) {
									this.YarnNewRoll = this.YarnNewRoll + aResultData[i].NewRoll;
								}
								if (parseFloat(aResultData[i].NewQty) != 0) {
									this.YarnNewQty = this.YarnNewQty + aResultData[i].NewQty;
								}
								if (parseFloat(aResultData[i].Roll) != 0) {
									this.YarnSumRoll = this.YarnSumRoll + aResultData[i].Roll;
								}
								if (parseFloat(aResultData[i].Qty) != 0) {
									this.YarnSumQty = this.YarnSumQty + aResultData[i].Qty;
								}
							};
							this.YarnOldRoll = this.YarnOldRoll.toFixed(2);
							this.YarnOldQty = this.YarnOldQty.toFixed(2);
							this.YarnNewRoll = this.YarnNewRoll.toFixed(2);
							this.YarnNewQty = this.YarnNewQty.toFixed(2);
							this.YarnSumRoll = this.YarnSumRoll.toFixed(2);
							this.YarnSumQty = this.YarnSumQty.toFixed(2);
						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.CheckDetailList = [];
						} else {
							this.CheckDetailList = [];
						}

					},
				})
			},
		}
	}
</script>

<style>
	page {
		background-color: #F8F8F8;
		padding-bottom: 260rpx;
	}

	.u-radio {
		width: 200rpx !important;
	}

	.submitView {
		width: 100%;
		padding: 16rpx 0 26rpx;
		background-color: #FFFFFF;
		position: fixed;
		bottom: 0;
		left: 0;
		border-top: 1rpx solid #f1f1f1;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 100;
	}

	.submitBtn {
		width: 666rpx;
	}

	.productBox {
		background-color: #FFFFFF;
		margin-top: 32rpx;
		padding: 26rpx 0;
	}

	.tjcpName {
		width: 686rpx;
		height: 40rpx;
		font-size: 16px;
		font-weight: bold;
		border-left: 6rpx solid #007AFF;
		padding-left: 12rpx;
		margin-left: 26rpx;
		margin-top: 26rpx;
	}

	.cpInput {
		width: 150rpx !important;
		margin-right: 12rpx;
	}

	.cpInput>input {
		box-sizing: border-box;
		border: 1rpx solid #DDDDDD;
		width: 100%;
		height: 60rpx;
		border-radius: 10rpx;
		padding: 0 10rpx;
	}

	.cpInput1 {
		width: 200rpx !important;
		margin-right: 12rpx;
	}

	.cpInput1>input {
		box-sizing: border-box;
		border: 1rpx solid #DDDDDD;
		width: 100%;
		height: 60rpx;
		border-radius: 10rpx;
		padding: 0 10rpx;
	}

	.clearIcon {
		position: absolute;
		right: 6rpx;
		top: 6rpx;
	}

	.greenPrice {
		font-size: 16px;
		color: #19BE6B !important;
		font-weight: bold;
	}

	.disFlex {
		display: flex;
		align-items: center;
		margin-bottom: 8rpx;
	}

	.inputName {
		color: #ADADAD;
		font-size: 16px;
	}

	.addHKQS {
		display: flex;
		align-items: center;
		padding: 16rpx 26rpx;
		font-size: 15px;
		font-weight: bold;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>
