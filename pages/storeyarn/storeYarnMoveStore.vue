<template>
	<view class="wrap">
		<u-form ref="uForm">
			<u-form-item label-width="150" label="调至仓位:">
				<input type="text" v-model="StoreStationNo" :focus="testFocus0" style="width:80px;" @confirm="GetStoreStationName" />
				<text class="title" style="width:80px;">名称：{{StoreStationName}}</text>
				<button class="mini-btn" type="primary" size="mini" @tap="ClearBillData">清空数据</button>
			</u-form-item>
			<u-form-item label-width="150" label="原料名称:">
				<text class="title" style="width:150px;">{{YarnGoodsName}}</text>
				<text class="title" style="width:150px;">缸号：{{YarnCrockNo}}</text>
			</u-form-item>
			<u-form-item label-width="150" label="色号颜色:">
				<text class="title" style="width:200px;">{{YarnGoodsCodeNo}} {{YarnGoodsCodeName}} {{YarnGoodsColorNo}}</text>
				<checkbox-group @change="ViewDetailChange">
					<checkbox ref="checkBoxRef" :checked="ViewDetailStatus">显示明细</checkbox>
				</checkbox-group>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:80px;">箱号：{{YarnBoxBillNo}}</text>
				<text class="title" style="width:90px;">个数：{{YarnBoxRoll}}</text>
				<text class="title" style="width:90px;">毛重：{{YarnGrossQty}}</text>
				<text class="title" style="width:90px;">净重：{{YarnNetQty}}</text>
			</u-form-item>
			<u-form-item label-width="150" label="条码资料:">
				<input type="text" v-model="QRBarCode" :focus="testFocus2" style="width:250px;"
					@confirm="CreateMoveStoreStation" />
			</u-form-item>
			<u-form-item>
				<text class="title">{{BillDataMessage}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:110px;">缸号件数：{{YarnCrockNoRoll}}</text>
				<text class="title" style="width:140px;">缸号毛重：{{YarnCrockNoGrossQty}}</text>
				<text class="title" style="width:140px;">缸号净重：{{YarnCrockNoNetQty}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:110px;">仓位件数：{{YarnBillSumRoll}}</text>
				<text class="title" style="width:140px;">仓位毛重：{{YarnBillSumGrossQty}}</text>
				<text class="title" style="width:140px;">仓位净重：{{YarnBillSumNetQty}}</text>
			</u-form-item>
		</u-form>

		<view class="u-demo-area">
			<u-toast ref="uToast"></u-toast>
			<wyb-table ref="table" :headers="headersMaster" :contents="YarnGoodsDetailDataList"/>
		</view>
	</view>
</template>
<script>
	import util, {
		parFabricGoodsBarCode2D,
		parYarnGoodsBarCode2D,
		playSuccessAudio,
		playErrorAudio
	} from '../../common/util';
	import wybTable from '@/components/wyb-table/wyb-table.vue';
	export default {
		data() {
			return {
				borderColor: '#e4e7ed',
				align: 'center',
				index: 0,
				YarnDetailList: [],
				QRBarCode: '',
				InStoreBillID: 0,
				InStoreBillNo: '',
				InStoreMasterNo: '',
				StoreNameID: 0,
				StoreStationNo: '',
				StoreStationName: '',
				DyeWorksDigCode: 0,
				YarnGoodsNo: '',
				YarnGoodsName: '',
				YarnGoodsCodeNo: '',
				YarnGoodsCodeName: '',
				YarnGoodsColorNo: '',
				YarnCrockNo: '',
				YarnBoxBillNo: '',
				YarnBoxRoll: '', //每箱个数
				YarnRoll: '',
				YarnGrossQty: '',
				YarnNetQty: '',
				YarnCrockNoRoll: '',
				YarnCrockNoGrossQty: '',
				YarnCrockNoNetQty: '',
				YarnBillSumRoll: '',
				YarnBillSumGrossQty: '',
				YarnBillSumNetQty: '',
				testFocus0: true, // TODO: 修改正确的变量名， 暂时未测试名字
				testFocus1: false,
				testFocus2: false,
				ViewDetailStatus: true,
				BillDataMessage: '',
				YarnGoodsDetailDataList:[],
				headersMaster: [{
				                label: '名称',
				                key: 'YarnName'
				            }, {
				                label: '色号',
				                key: 'YarnColorNo'
				            }, {
				                label: '颜色',
				                key: 'YarnColorName'
				            }, {
				                label: '缸号',
				                key: 'YarnCrockNo'
				            }, {
				                label: '件数',
				                key: 'Roll'
				            }, {
				                label: '重量',
				                key: 'Qty'
				            }],
			};
		},
		methods: {
			playSuccess() {
				util.playSuccessAudio();
			},
			playError() {
				util.playErrorAudio();
			},
			ViewDetailChange: function() {
				this.ViewDetailStatus = !this.ViewDetailStatus;
				if (this.ViewDetailStatus==true)
				{
					this.GetDetailListData();
				}
				else
				{
					this.YarnGoodsDetailDataList = [];
				}
			},
			borderColorChange(index) {
				this.borderColor = index == 0 ? '#e4e7ed' : index == 1 ? '#2979ff' : '#ff9900';
			},
			alignChange(index) {
				this.align = index == 0 ? 'left' : index == 1 ? 'center' : 'right';
			},
			GetStoreStationName: function() {
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetStoreStationName',
							params: [{
								name: 'No',
								value: this.StoreStationNo
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							this.playSuccess();
							var aResultData = res.data.data;
							for (var i = 0; i < aResultData.length; i++) {
								this.StoreStationName = aResultData[i].StoreStationName;
								this.StoreNameID = parseInt(aResultData[i].StoreNameID);
							};
							this.GetDetailListData();
							this.testFocus0 = false;
							this.$nextTick(() => {
								this.testFocus2 = true;
								this.testFocus0 = false;
							});
						} else {
							this.playError();
							this.StoreStationName = "",
								this.StoreNameID = 0,
								this.BillDataMessage = '不存在当前的仓位资料，请确认！';
						}
					},
					fail: (error) => {
						this.playError();
						this.BillDataMessage = '连接服务器出错，请检查后台服务是否启动！' + res.data.msg;
					},
				})
			},
			ClearBillData: function() {
				this.testFocus0 = false;
				this.$nextTick(() => {
					this.testFocus0 = true;
				});
				this.YarnDetailList = [];
				this.QRBarCode = "";
				this.InStoreBillID = 0;
				this.InStoreBillNo  = "";
				this.InStoreMasterNo = "";
				this.StoreNameID = 0;
				this.StoreStationNo = "";
				this.StoreStationName = "";
				this.DyeWorksDigCode = 0;
				this.YarnGoodsNo = "";
				this.YarnGoodsName = "";
				this.YarnGoodsCodeNo = "";
				this.YarnGoodsCodeName = "";
				this.YarnGoodsColorNo = "";
				this.YarnCrockNo = "";
				this.YarnBoxBillNo = "";
				this.YarnBoxRoll = "";
				this.YarnRoll = "";
				this.YarnGrossQty = "";
				this.YarnNetQty = "";
				this.YarnCrockNoRoll = "";
				this.YarnCrockNoGrossQty = "";
				this.YarnCrockNoNetQty = "";
				this.YarnBillSumRoll = 0;
				this.YarnBillSumGrossQty = 0;
				this.YarnBillSumNetQty = 0;
				this.BillDataMessage = "";
				this.YarnGoodsDetailDataList = [];
				/*
				this.StoreStationNo = "";
				this.BillNo = "";
				this.StoreStationName = "";
				this.FabricGoodsNo = "";
				this.FabricGoodsName = "";
				this.GoodsCodeNo = "";
				this.GoodsCodeName = "";
				this.CrockNo = "";
				this.GoodsBillNo = "";
				this.BarCodeDig = "";
				this.BarCodeQty = "";
				this.CrockNoSumRoll = "";
				this.CrockNoSumQty = "";
				this.StationNameSumRoll = "";
				this.StationNameSumQty = "";
				this.BillSumRoll = "";
				this.BillSumQty = "";
				this.BarCode = '';
				*/
			},

			GetDetailListData: function() {
				this.CarryDetailList = [];
				this.BillID = 0;
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP_GetStoreYarnStoreStationDetailSQL',
							params: [{
								name: 'StationName',
								value: this.StoreStationName
							}]
						},
					},
					success: (res) => {
						console.log("--->>>" + JSON.stringify(res.data));
						if (res.data.status == 0 && res.data.count > 0) {
							this.playSuccess();
							var aResultData = res.data.data;

							this.YarnGoodsDetailDataList = [];
							this.YarnBillSumRoll = 0;
							this.YarnBillSumNetQty= 0;
							this.YarnBillSumGrossQty = 0;
							for (var i = 0; i < aResultData.length; i++) {
								this.YarnGoodsDetailDataList.push(aResultData[i]);
								this.InStoreBillID = aResultData[i].BillMasterID;
								this.YarnBillSumRoll = this.YarnBillSumRoll + parseFloat(aResultData[i].Roll);
								this.YarnBillSumGrossQty = this.YarnBillSumGrossQty + parseFloat(aResultData[i].GrossQty);
								this.YarnBillSumNetQty = this.YarnBillSumNetQty + parseFloat(aResultData[i].NetQty);
							};
							this.YarnBillSumGrossQty = this.YarnBillSumGrossQty.toFixed(2);
							this.YarnBillSumNetQty = this.YarnBillSumNetQty.toFixed(2);
							this.testFocus2 = false;
							this.$nextTick(() => {
								this.testFocus0 = false;
								this.testFocus1 = false;
								this.testFocus2 = true;
							});
						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.playError();
							this.testFocus2 = false;
							this.$nextTick(() => {
								this.testFocus0 = false;
								this.testFocus1 = false;
								this.testFocus2 = true;
							});
						} else {
							this.playError();
							this.testFocus2 = false;
							this.$nextTick(() => {
								this.testFocus0 = false;
								this.testFocus1 = false;
								this.testFocus2 = true;
							});
						}
					},
					fail: (error) => {
						this.playError();
						this.testFocus2 = false;
						this.$nextTick(() => {
							this.testFocus2 = true;
						});
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},

			CreateMoveStoreStation() {
				this.testFocus2 = false;
				this.$nextTick(() => {
					this.testFocus0 = false;
					this.testFocus1 = false;
					this.testFocus2 = true;
				});

				if (this.StoreNameID == 0 && this.InStoreMasterNo == '') {
					this.playError();
					this.BillDataMessage = '请先新增单据';
					return;
				}

				var aStrBarCode = parYarnGoodsBarCode2D(this.QRBarCode);
				aStrBarCode = aStrBarCode.trimRight().trimLeft();
				if (aStrBarCode == '') {
					this.playError();
					this.QRBarCode = '';
					this.testFocus2 = true;
					this.BillDataMessage = '无效的二维码！';
					return;
				}
				console.log("-------->" + aStrBarCode + "<----" + this.InStoreBillID);
				var aBarCodeList = aStrBarCode.split(',');
				this.DyeWorksDigCode = aBarCodeList[0];
				this.YarnGoodsCodeNo = aBarCodeList[2];
				this.YarnGoodsColorNo = aBarCodeList[3];
				this.YarnCrockNo = aBarCodeList[5];
				this.YarnBoxBillNo = aBarCodeList[6];
				this.YarnBoxRoll = aBarCodeList[7];
				this.YarnGrossQty = aBarCodeList[8];
				this.YarnNetQty = aBarCodeList[9];
				uni.request({
					url: util.apiurl + 'rest/db/storedproc',
					data: {
						token: getApp().globalData.Token,
						data: {
							db_name: getApp().globalData.AppDBName,
							proc_name: 'Usp_APP_StoreYarnCreateMoveStation',
							method: 'open_proc',
							params: [{
									name: '@BillID',
									value: this.InStoreBillID
								},
								{
									name: '@BillTypeID',
									value: '0'
								},
								{
									name: '@StoreStationName',
									value: this.StoreStationNo
								},
								{
									name: '@QRBarCode',
									value: aStrBarCode
								},
								{
									name: '@UserName',
									value: getApp().globalData.UserName
								}
							]
						},
					},
					success: (res) => {
						if (res.data.status == 0) {
							console.log(res.data.data.toString());
							var aResultData = JSON.parse(res.data.data);
							if (aResultData.BillDataStatus == '0' && aResultData.BillDataMessage ==
								'SUCCESS') {
								this.playSuccess();
								this.YarnGoodsName = aResultData.YarnGoodsName;
								this.YarnGoodsCodeName = aResultData.YarnGoodsCodeName;
								this.YarnCrockNoRoll = parseFloat(aResultData.YarnCrockNoRoll);
								this.YarnCrockNoGrossQty = parseFloat(aResultData.YarnCrockNoGrossQty);
								this.YarnCrockNoNetQty = parseFloat(aResultData.YarnCrockNoNetQty);
								this.YarnBillSumRoll = parseFloat(aResultData.YarnBillSumRoll);
								this.YarnBillSumGrossQty = parseFloat(aResultData.YarnBillSumGrossQty);
								this.YarnBillSumNetQty = parseFloat(aResultData.YarnBillSumNetQty);
								this.QRBarCode = '';
								this.BillDataMessage = '扫描成功！';
								if (this.ViewDetailStatus == true){
									this.GetDetailListData();
								}
								this.testFocus2 = false;
								this.$nextTick(() => {
									this.testFocus0 = false;
									this.testFocus1 = false;
									this.testFocus2 = true;
								});
							} else {
								this.playError();
								this.BillDataMessage = '入仓出错，' + aResultData.BillDataMessage;
								this.QRBarCode = '';
								this.testFocus2 = false;
								this.$nextTick(() => {
									this.testFocus0 = false;
									this.testFocus1 = false;
									this.testFocus2 = true;
								});
								return;
							}

						} else {
							this.playError();
							this.QRBarCode = '';
							this.BillDataMessage = '入仓出错，' + res.data.msg;
							this.testFocus2 = false;
							this.$nextTick(() => {
								this.testFocus0 = false;
								this.testFocus1 = false;
								this.testFocus2 = true;
							});
							return;
						}
					},
					fail: (error) => {
						this.playError();
						this.QRBarCode = '';
						this.testFocus2 = false;
						this.$nextTick(() => {
							this.testFocus0 = false;
							this.testFocus1 = false;
							this.testFocus2 = true;
						});
						this.BillDataMessage = '入仓出错，' + res.data.msg;
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			}
		},
	}
</script>
<style>
	button {
		margin-top: 30upx;
		margin-bottom: 30upx;
	}

	.button-sp-area {
		margin: 0 auto;
		width: 60%;
	}

	.mini-btn {
		margin-right: 10upx;
	}
</style>
