<template>
  <view class="scan-page">
    <!-- 相机组件 -->
    <camera 
      class="camera" 
      mode="scanCode" 
      :flash="flashLight" 
      @scancode="handleScanCode"
      :device-position="devicePosition"
    ></camera>

    <!-- 自定义导航栏 -->
    <common-navbar backgroundColor="#FFFFFF">
      <template #left>
        <view class="nav-btn" @click="handleBack">
          <text class="iconfont icon-close" style="color: #333333;">关闭</text>
        </view>
      </template>
      <template #center>
        <text style="color: #333333; font-size: 18px;">二维码</text>
      </template>
      <template #right>
        <view class="nav-btn" @click="chooseImage">
          <text style="color: #333333;">相册</text>
        </view>
      </template>
    </common-navbar>

    <!-- 扫码区域 -->
    <view class="scan-area">
      <!-- 扫描框 -->
      <view class="scan-box">
        <!-- 网格背景 -->
        <view class="grid-background"></view>
        <!-- 红色边框 -->
        <view class="scan-border"></view>
        <!-- 扫描线 -->
        <view class="scan-line"></view>
      </view>
      
      <!-- 底部提示文字 -->
      <view class="bottom-tips">
        <text>请将二维码放入框内，即可自动扫描</text>
      </view>
    </view>
  </view>
</template>

<script>
import CommonNavbar from '@/components/common-navbar/index.vue';

export default {
  components: {
    CommonNavbar
  },
  
  data() {
    return {
      isScanning: true,
      flashLight: false, // 闪光灯状态
      devicePosition: 'back', // 相机朝向
    }
  },
  
  methods: {
    handleBack() {
      uni.navigateBack();
    },
    
    chooseImage() {
      uni.chooseImage({
        count: 1,
        sourceType: ['album'],
        success: (res) => {
          // 处理选择的图片
          this.handleImageScan(res.tempFilePaths[0]);
        }
      });
    },

    // 处理扫码结果
    handleScanCode(e) {
      const result = e.detail.result;
      console.log('扫码结果：', result);
      // 这里处理扫码结果
      uni.navigateBack({
        success: () => {
          // 可以通过事件总线或其他方式将结果传回上一页
          uni.$emit('scanResult', result);
        }
      });
    },

    // 处理图片扫码
    handleImageScan(imagePath) {
      uni.showLoading({ title: '正在识别' });
      // 这里添加图片扫码的逻辑
    },

    // 切换闪光灯
    toggleFlashLight() {
      this.flashLight = !this.flashLight;
    }
  }
}
</script>

<style lang="scss" scoped>
.scan-page {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: #000;
}

.camera {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1;
}

.scan-area {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  padding-top: 150px; // 调整扫描框位置到中偏上
}

.scan-box {
  position: relative;
  width: 280px;
  height: 280px;
  
  .grid-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(0deg, transparent 24%, rgba(255, 0, 0, .3) 25%, rgba(255, 0, 0, .3) 26%, transparent 27%, transparent 74%, rgba(255, 0, 0, .3) 75%, rgba(255, 0, 0, .3) 76%, transparent 77%, transparent),
                      linear-gradient(90deg, transparent 24%, rgba(255, 0, 0, .3) 25%, rgba(255, 0, 0, .3) 26%, transparent 27%, transparent 74%, rgba(255, 0, 0, .3) 75%, rgba(255, 0, 0, .3) 76%, transparent 77%, transparent);
    background-size: 30px 30px;
  }

  .scan-border {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 1px solid #ff0000;
  }

  .scan-line {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(to right, transparent, #ff0000, transparent);
    animation: scanMove 2s linear infinite;
  }
}

.bottom-tips {
  position: absolute;
  bottom: 300px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  
  text {
    color: #fff;
    font-size: 14px;
    padding: 12px 24px;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 4px;
  }
}

.nav-btn {
  padding: 8px;
}

@keyframes scanMove {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(250px);
  }
}
</style>