<template>
	<view>
		<view class="u-demo-block">
			<view class="u-page__tag-item">
				<u-search v-model="searchCustomerName" :show-action="false" @custom="selectKehuFun"
					 @search="selectKehuFun" placeholder="请输入客户名称"></u-search>
			</view>
		</view>
		<scroll-view scroll-y="true" :style="{height: scrollHeight}" @scrolltolower="selectKehuFun" refresher-enabled
			:refresher-threshold="200" :refresher-triggered="triggered" refresher-background="gray"
			@refresherrefresh="onRefresh" @refresherrestore="onRestore">
		<view v-if="list.length > 0">
			<view v-for="(item, index) in list" :key="index" @click="khCardClickFun(item)">
				<khCard :item="item" :isSelect="isSelect" :index="index" @cxGetDataFun="cxGetDataFun"></khCard>
			</view>
		</view>
		</scroll-view>
		<u-action-sheet :list="sheetList" v-model="moreShow"></u-action-sheet>
	</view>
</template>

<script>
	let that = this;
	import dataNull from '@/components/dataNull/dataNull.vue'
	import addBtn from '@/components/addBtn/addBtn.vue'
	import getMore from '@/components/getMore/getMore.vue'
	import topDropdown from '../../../components/topDropdown/topDropdown.vue'
	import lianxiRow from '../../../components/lianxiRow/lianxiRow.vue'
	import khCard from '../../../components/card/kehu.vue'
	import util, {
		playSuccessAudio,
		playErrorAudio
	} from '../../../common/util.js';
	export default {
		components: {
			dataNull,
			addBtn,
			getMore,
			topDropdown,
			lianxiRow,
			khCard
		},
		data() {
			return {
				triggered: false,
				searchCustomerName: '',
				CustomerTypes: '',
				list: [],
				moreShow: false,
				isMore: true,
				pageIndex: 1,
				scrollHeight: '667px',
				sheetList: [{
						text: '添加跟进'
					},
					{
						text: '添加标签'
					}
				],
			}
		},
		onLoad(e) {
			that = this;
			uni.getSystemInfo({
				success(res) {
					that.scrollHeight = res.windowHeight - 40 + 'px';
				}
			})
			this.CustomerTypes = e.type;
			console.log(">>>" + e.type);
			if (e.type) {
				that.isSelect = true;
			}
			this.selectKehuFun();
		},
		methods: {
			// 查询用户
			selectKehuFun: function() {
				console.log("---->>>" + this.searchCustomerName);
				let aSaleUserID = 0;
				if (getApp().globalData.IsSaleUserStatus == 1)
					aSaleUserID = getApp().globalData.EmployeeID;
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.BaseData.GetCustomerData',
							params: [{
								name: 'SUID',
								value: aSaleUserID
							}, {
								name: 'MUID',
								value: aSaleUserID
							}, {
								name: 'CName',
								value: '%' + this.searchCustomerName + '%'
							}, {
								name: 'CustomerTypes',
								value: '%' + this.CustomerTypes + '%'
							}]
						},
					},
					success: (res) => {
						let data = res.data.data;
						console.log("---->>>" + JSON.stringify(res.data.data));
						that.list = that.list.concat(data);
					},
				})
			},
			
			
			// 搜索框回调方法
			searchBoxEmitFun: function(e) {
				that.searchValue = e.searchValue1;
				that.cxGetDataFun()
			},
			// 下拉刷新
			onRefresh: function() {
				if (that.triggered) return
				that.triggered = true;
				that.cxGetDataFun();
			},
			onRestore: function(e) {
				that.triggered = false; // 需要重置
			},
			// 重新获取数据
			cxGetDataFun: function() {
				that.pageIndex = 1;
				that.isMore = true;
				that.selectKehuFun();
			},
			
			// 客户点击方法
			khCardClickFun: function(item) {
				if (that.pageType == 'lxr') {
					uni.$emit('kehuBindFun', {
						clientName: item.clientName,
						clientId: item._id
					})
					uni.navigateBack()
				} else if (that.pageType == 'addGjjl') {
					uni.$emit('gjKehuBindFun', {
						clientName: item.clientName,
						clientId: item._id
					})
					uni.navigateBack()
				} else if (that.pageType == 'sj') {
					uni.$emit('sjKehuBindFun', {
						clientName: item.clientName,
						clientId: item._id
					})
					uni.navigateBack()
				} else if (that.pageType == 'saleallocated') {
					uni.$emit('bjdKehuBindFun', {
						CustomerID: item.CustomerID,
						CustomerName: item.CustomerName,
						PlanDepartmentID: item.PlanDepartmentID,
						PlanDepartmentName: item.PlanDepartmentName,
						SaleUserID: item.SaleUserID,
						SaleUserName: item.SaleUserName,
						Remark: item.Remark,
						CustomerAddress: item.CustomerAddress,
						CustomerPhone: item.CustomerPhone,
						CustomerLinkName: item.CustomerLinkName,
					})
					uni.navigateBack()
				} else if (that.pageType == 'heTong') {
					uni.$emit('heTongdKehuBindFun', {
						clientName: item.clientName,
						clientId: item._id
					})
					uni.navigateBack()
				} else if (that.pageType == 'topDropDown') {
					uni.$emit('topDropDownBindFun', {
						clientName: item.clientName,
						clientId: item._id
					})
					uni.navigateBack()
				} else if (that.pageType != '') {
					uni.$emit('kehuBindFun', item)
					uni.navigateBack()
				}
			},
		}
	}
</script>

<style>
	page {
		background-color: #F8F8F8;
	}

	.card {
		width: 698rpx;
		padding: 26rpx 26rpx 10rpx;
		margin: 32rpx 26rpx;
		box-sizing: border-box;
		border-radius: 8rpx;
		box-shadow: #d8d8d8 0px 0px 16rpx;
		position: relative;
		background-color: #FFFFFF;
	}

	.genjinBtn {
		position: absolute;
		right: 26rpx;
		top: 26rpx;
		background-color: #007AFF;
		color: #FFFFFF;
		text-align: center;
		padding: 6rpx 16rpx;
		border-radius: 6rpx;
		font-size: 14px;
	}

	.genjinBtn:active {
		background-color: #13B8FF;
	}

	.topRow {
		display: flex;
		align-items: center;
		margin-bottom: 16rpx;
	}

	.txView {
		width: 128rpx;
		height: 128rpx;
		border-radius: 50%;
		overflow: hidden;
		margin-right: 26rpx;
	}

	.txViewImg {
		width: 100%;
		height: 100%;
	}

	.info {
		width: 492rpx;
		font-size: 15px;
		color: #000;
	}

	.name {
		font-size: 16px;
		margin-bottom: 8rpx;
		color: #000000;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		font-weight: bold;
	}

	.bottomRow {
		width: 100%;
		padding-top: 16rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		border-top: 1rpx solid #f0f0f0;
		font-size: 15px;
		color: #007AFF;
	}

	.lxRow {
		display: flex;
		align-items: center;
	}

	.lxRow>image {
		width: 52rpx;
		height: 52rpx;
		margin-right: 26rpx;
	}

	.bqRow {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
	}

	.bqRow>text {
		font-size: 14px;
		color: #888888;
	}

	.bqRow>view {
		padding: 6rpx 16rpx;
		font-size: 14px;
		background-color: rgba(255, 85, 127, 0.1);
		color: #ff5500;
		border-radius: 10rpx;
		margin: 6rpx 26rpx 20rpx 0;
	}
</style>
