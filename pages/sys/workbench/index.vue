<template>
  <view class="wrap">
    <common-navbar title="工作台" :showBack="false">
      <template #right>
        <view class="scan_box">
          <u-icon
            name="scan"
            color="#333333"
            size="46"
            @click="handleScan"
          ></u-icon>
        </view>
      </template>
    </common-navbar>
    <view class="workbench-title">销售管理</view>
    <view class="toolbar">
      <u-grid class="grid" :col="4" :border="false">
        <u-grid-item :index="0" @click="navTo('/pages/saleship/salepickscan')">
          <view class="home-icon icon-color05">
            <i class="iconfont icon-hetongguanli"></i>
          </view>
          <view class="grid-text">成品配布</view>
        </u-grid-item>
        <u-grid-item
          :index="1"
          @click="navTo('/pages/storegoods/QRBarCodeReview')"
        >
          <view class="home-icon icon-color02">
            <i class="iconfont icon-mall-bag"></i>
          </view>
          <view class="grid-text">标签检测</view>
        </u-grid-item>
      </u-grid>
    </view>
    <view class="workbench-title">成品管理</view>
    <view class="toolbar">
      <u-grid class="grid" :col="4" :border="false">
        <u-grid-item
          :index="0"
          @click="navTo('/pages/storegoods/dyeworksDyeback')"
        >
          <view class="home-icon icon-color04">
            <i class="iconfont icon-finance"></i>
          </view>
          <view class="grid-text">染整进仓</view>
        </u-grid-item>
        <u-grid-item
          :index="1"
          @click="navTo('/pages/storegoods/storeGoodsBusinessIn')"
        >
          <view class="home-icon icon-color03">
            <i class="iconfont icon-mall-bag"></i>
          </view>
          <view class="grid-text">成品进仓</view>
        </u-grid-item>
        <u-grid-item
          :index="2"
          @click="navTo('/pages/storegoods/storeGoodsBusinessOut')"
        >
          <view class="home-icon icon-color12">
            <i class="iconfont icon-baoxiaodan"></i>
          </view>
          <view class="grid-text">成品出仓</view>
        </u-grid-item>
        <u-grid-item
          @click="navTo('/pages/storegoods/storeGoodsBusinessCheck')"
        >
          <view class="home-icon icon-color04">
            <i class="iconfont icon-shenpi"></i>
          </view>
          <view class="grid-text">成品盘点</view>
        </u-grid-item>
        <u-grid-item
          @click="navTo('/pages/storegoods/storeGoodsBusinessStationMoveOnly')"
        >
          <view class="home-icon icon-color04">
            <i class="iconfont icon-shenpi"></i>
          </view>
          <view class="grid-text">成品移架</view>
        </u-grid-item>
        <u-grid-item @click="navTo('/pages/storegoods/storegoodsstoresearch')">
          <view class="home-icon icon-color04">
            <i class="iconfont icon-shenpi"></i>
          </view>
          <view class="grid-text">成品库存</view>
        </u-grid-item>
        <u-grid-item @click="navTo('/pages/storegoods/storegoodsstoreallin')">
          <view class="home-icon icon-color04">
            <i class="iconfont icon-shenpi"></i>
          </view>
          <view class="grid-text">进仓查询</view>
        </u-grid-item>
        <u-grid-item @click="navTo('/pages/storegoods/storegoodsstoreallout')">
          <view class="home-icon icon-color04">
            <i class="iconfont icon-shenpi"></i>
          </view>
          <view class="grid-text">出仓查询</view>
        </u-grid-item>
      </u-grid>
    </view>
    <!--
		<view class="workbench-title">坯布管理</view>
		<view class="toolbar">
			<u-grid class="grid" :col="4" :border="false">
				<u-grid-item :index="0" @click="navTo('/pages/storefabric/storefabricInStore')">
					<view class="home-icon icon-color04">
						<i class="iconfont icon-hetongguanli"></i>
					</view>
					<view class="grid-text">坯布进仓</view>
				</u-grid-item>
				<u-grid-item :index="1" @click="navTo('/pages/storefabric/storefabricOutStore')">
					<view class="home-icon icon-color03">
						<i class="iconfont icon-chucha"></i>
					</view>
					<view class="grid-text">坯布出仓</view>
				</u-grid-item>
				<u-grid-item :index="2" @click="navTo('')">
					<view class="home-icon icon-color12">
						<i class="iconfont icon-ribao"></i>
					</view>
					<view class="grid-text">坯布盘点</view>
				</u-grid-item>
				<u-grid-item  @click="navTo('')">
					<view class="home-icon icon-color04">
						<i class="iconfont icon-tongzhi"></i>
					</view>
					<view class="grid-text">坯布移架</view>
				</u-grid-item>
				<u-grid-item  @click="navTo('/pages/storefabric/storefabricstoresearch')">
					<view class="home-icon icon-color05">
						<i class="iconfont icon-huiyishi"></i>
					</view>
					<view class="grid-text">坯布库存</view>
				</u-grid-item>
				<u-grid-item  @click="navTo('/pages/storefabric/storefabricstoreallin')">
					<view class="home-icon icon-color05">
						<i class="iconfont icon-huiyishi"></i>
					</view>
					<view class="grid-text">进仓查询</view>
				</u-grid-item>
				<u-grid-item  @click="navTo('/pages/storefabric/storefabricstoreallout')">
					<view class="home-icon icon-color05">
						<i class="iconfont icon-huiyishi"></i>
					</view>
					<view class="grid-text">出仓查询</view>
				</u-grid-item>
			</u-grid>
		</view>
		-->
  </view>
</template>
<script>
import CommonNavbar from "@/components/common-navbar/index";
/**
 * Copyright (c) 2013-Now http://aidex.vip All rights reserved.
 */
export default {
  components: {
    CommonNavbar,
  },
  data() {
    return {
      show: false,
      head: "/static/aidex/images/head.png",
      imgList: [
        { image: "/static/aidex/banner/banner01.png" },
        //{image: '/static/aidex/banner/banner02.png'},
        //{image: '/static/aidex/banner/banner03.png'}
      ],
      todoCount: 3,
      scanReceiver: null,
      isPageActive: false,
    };
  },

  onLoad() {
    // #ifdef APP-PLUS
    this.isPageActive = true;
    this.registerScanBroadcast();
    // #endif
  },

  onUnload() {
    // #ifdef APP-PLUS
    this.isPageActive = false;
    this.unregisterBroadcast();
    // #endif
  },

  onHide() {
    // 页面隐藏时
    this.isPageActive = false;
  },

  onShow() {
    // 页面显示时
    this.isPageActive = true;
  },

  computed: {
    contentStyle() {
      const navHeight = getStatusBarHeight() + 44;
      return {
        height: `${navHeight}px`,
      };
    },
  },
  methods: {
    navTo(url) {
      uni.navigateTo({
        url: url,
      });
    },
    imgListClick(index) {
      console.log(`点击了第${index + 1}页图片`);
    },
    itemClick(index) {
      console.log(index);
    },
    // 处理扫码
    handleScan() {
    //   uni.navigateTo({
		  // 	url: '/pages/saleship/salepickscandetail?order_no=FPD-PB-202412270196'
		  // })
      // return
      // #ifdef APP-PLUS || MP-WEIXIN
      uni.scanCode({
        scanType: ['qrCode', 'barCode'], // 同时支持二维码和条形码
        success: (res) => {
          console.log('扫码成功：', res);
          const cleanResult = res.result.trim().replace(/[\r\n]/g, '');
		  uni.navigateTo({
		  	url: '/pages/saleship/salepickscandetail?order_no=' + cleanResult
		  })
        },
        fail: (err) => {
          console.error('扫码失败：', err);
          uni.showToast({
            title: '扫码失败',
            icon: 'none'
          });
        }
      });
      // #endif

      // #ifdef H5
      uni.showToast({
        title: "H5环境不支持扫码功能",
        icon: "error",
      });
      // #endif
    },
    // 处理扫码成功
    handleScanSuccess(result) {
      try {
        // 这里可以根据实际需求处理扫码结果
        // 例如：验证扫码结果的格式或内容
        if (this.validateScanResult(result)) {
          // 扫码验证通过，执行其他逻辑
          uni.showToast({
            title: "执行成功",
            icon: "success",
          });
        } else {
          uni.showToast({
            title: "无效的二维码",
            icon: "none",
          });
        }
      } catch (error) {
        console.error("处理扫码结果失败：", error);
        uni.showToast({
          title: "处理失败",
          icon: "none",
        });
      }
    },
    // 注册扫码广播
    registerScanBroadcast() {
      try {
        console.log('开始注册扫码广播');
        const main = plus.android.runtimeMainActivity();
        
        // 配置扫码枪广播
        try {
          const Intent = plus.android.importClass('android.content.Intent');
          const intent = new Intent('com.android.scanner.service_settings');
          intent.putExtra('action_barcode_broadcast', 'com.android.server.scannerservice.broadcast');
          intent.putExtra('key_barcode_broadcast', 'scannerdata');
          main.sendBroadcast(intent);
          console.log('扫码枪广播配置已发送');
        } catch (error) {
          console.error('配置扫码枪广播失败：', error);
        }

        // 注册广播接收器
        const IntentFilter = plus.android.importClass('android.content.IntentFilter');
        const filter = new IntentFilter();
        filter.addAction('com.android.server.scannerservice.broadcast');
        
        const receiver = plus.android.implements('io.dcloud.feature.internal.reflect.BroadcastReceiver', {
          onReceive: (context, intent) => {
            // 只有当页面活动时才处理广播
            if (!this.isPageActive) return;
            
            try {
              const scanResult = intent.getStringExtra('scannerdata');
              console.log('工作台-扫码结果:', scanResult);
              
              if (scanResult) {
                this.$nextTick(() => {
                  const cleanResult = scanResult.trim().replace(/[\r\n]/g, '');
                  uni.navigateTo({
                    url: '/pages/saleship/salepickscandetail?order_no=' + cleanResult
                  });
                });
              }
            } catch (error) {
              console.error('处理广播数据时出错：', error);
            }
          }
        });

        // 注册广播接收器
        main.registerReceiver(receiver, filter);
        this.scanReceiver = receiver;
        console.log('扫码广播注册成功，等待扫码...');
        
      } catch (error) {
        console.error('注册扫码广播失败：', error);
        console.error('错误详情：', error.message);
        console.error('错误堆栈：', error.stack);
      }
    },

    // 注销广播接收器
    unregisterBroadcast() {
      if (this.scanReceiver) {
        try {
          const main = plus.android.runtimeMainActivity();
          main.unregisterReceiver(this.scanReceiver);
          this.scanReceiver = null;
          console.log('扫码广播注销成功');
        } catch (error) {
          console.error('注销扫码广播失败：', error);
        }
      }
    },
  },
};
</script>
<style lang="scss">
@import "index.scss";
.scan_box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .scan_text {
    font-size: 12px;
    color: #2979ff;
  }
}
.banner-box {
  padding: 0 2%;
  width: 96%;
  height: 170rpx;
  margin: 30rpx 0 30rpx;
}
.u-swiper-wrap {
  padding: 0 10px;
}

.banner-pic {
  width: 47%;
  float: left;
  display: inline-block;
  margin: 0 1.5%;
}
.banner-pic image {
  width: 100%;
  height: 170rpx;
  border-radius: 12rpx;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
}

.u-mode-light-info {
  background-color: #ffffff;
  color: #666666;
  border: 1px solid #e9ecf6;
  font-size: 12px;
  padding: 2px 8px;
  position: relative;
  top: -3px;
}
.workbench-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  padding: 15px 30rpx;
}
.home-icon i.icon-tongzhi {
  font-size: 22px;
}
</style>
