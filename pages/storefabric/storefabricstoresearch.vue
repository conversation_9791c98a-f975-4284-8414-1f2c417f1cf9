<template>
	<view class="wrap">
		<u-form ref="uForm">
			<u-form-item label="仓库名称" label-width="150" prop="StoreName">
				<u-input type="select" :select-open="actionSheetShow" v-model="StoreName"
					placeholder="请选择仓库" @click="actionSheetShow = true"></u-input>
				<button class="mini-btn" type="primary" size="mini" @tap="ClearStoreNameData">清空</button>
			</u-form-item>
			<u-form-item>

				<common-select type="radio":popupTitle="popupTitle" name="cworkStationName"
				:dataLists="FabricdataLists" placeholder="输入工站名称搜索" @cancel="show=false" @search="selectSearch"
				@submit=""> </common-select>


				<u-search shape="round" v-model="FabricGoodsName" placeholder="请输入布类" actionText=""></u-search>
			</u-form-item>
			<u-form-item>
				<u-search shape="round" v-model="GoodsCodeName" placeholder="请输入颜色" actionText=""></u-search>
				<button class="mini-btn" type="primary" size="mini" @tap="GetDetailListData">查询</button>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">总条数：{{FabricSumRoll}}</text>
				<text class="title" style="width:200px;">总重量：{{FabricSumQty}}</text>
			</u-form-item>
		</u-form>
		<view class="u-demo-area">
			<u-toast ref="uToast"></u-toast>
			<wyb-table ref="table" :headers="headersMaster" :contents="FabricGoodsDataList"
				@onCellClick="GetDetailCrockNoListData" />
		</view>
		<u-action-sheet :list="StoreNameDataList" v-model="actionSheetShow" @click="actionSheetCallback">
		</u-action-sheet>
	</view>
</template>

<script>
	import util, {
		parFabricGoodsBarCode2D
	} from '../../common/util';
	import wybTable from '@/components/wyb-table/wyb-table.vue';
	//import {btable} from '@/components/qianziyu-select/qianziyu-select.vue';
	export default {
		name: "btable",
		data() {
			return {
				borderColor: '#e4e7ed',
				align: 'center',
				index: 0,
				actionSheetShow: false,
				StoreName: '',
				FabricGoodsName: '',
				GoodsCodeName: '',
				FabricSumRoll:0,
				FabricSumQty:0,
				FabricGoodsDataList: [],
				GoodsCrockNoDataList: [],
				FabricdataLists:[],
				StoreNameDataList: [],
				popupTitle: '',
				headersMaster: [{
					label: '编号',
					key: 'FabricGoodsNo'
				},{
					label: '名称',
					key: 'FabricGoodsName'
				}, {
					label: '色号',
					key: 'GoodsCodeNo'
				}, {
					label: '颜色',
					key: 'GoodsCodeName'
				}, {
					label: '条数',
					key: 'Roll'
				}, {
					label: '数量',
					key: 'Qty'
				}, {
					label: '单位',
					key: 'UnitName'
				}, {
					label: '仓库',
					key: 'StoreName'
				}],
				headersDetail: [{
					label: '名称',
					key: 'FabricGoodsName'
				}, {
					label: '色号',
					key: 'GoodsCodeNo'
				}, {
					label: '颜色',
					key: 'GoodsCodeName'
				}, {
					label: '缸号',
					key: 'CrockNo'
				}, {
					label: '条数',
					key: 'Roll'
				}, {
					label: '数量',
					key: 'Qty'
				}]

			}
		},
		onLoad() {
			this.getStoreNameData();
		},
		methods: {
			upx2px(value) {
				//#ifndef MP-WEIXIN
				return uni.upx2px(value) + 'px'
				//#endif
				//#ifdef MP-WEIXIN
				return uni.upx2px(value)
				//#endif
			},
			rowClick(e) {
				console.log(e)
			},
			pullup() {
				console.log('上拉')
			},
			// 点击actionSheet回调
			actionSheetCallback(index) {
				uni.hideKeyboard();
				this.StoreName = this.StoreNameDataList[index].text;
			},

			getStoreNameData() {
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetStoreNameList',
							params: [{
								name: 'TypeNo',
								value: '%StoreFabricEmbryo%'
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							var aResultData = res.data.data;
							this.StoreNameDataList = res.data.data;
						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.StoreNameDataList = [];
						} else {
							this.StoreNameDataList = [];
						}
					},
					fail: (error) => {
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},

			ClearStoreNameData() {
				this.StoreName = '';
			},

			GetDetailListData: function() {
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetStoreFabricMasterSQL',
							params: [{
								name: 'StoreName',
								value: '%' + this.StoreName + '%'
							}, {
								name: 'FGNo',
								value: '%' + this.FabricGoodsName + '%'
							}, {
								name: 'FGName',
								value: '%' + this.FabricGoodsName + '%'
							}, {
								name: 'GCNO',
								value: '%' + this.GoodsCodeName + '%'
							}, {
								name: 'GCName',
								value: '%' + this.GoodsCodeName + '%'
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							var aResultData = res.data.data;
							this.FabricGoodsDataList = res.data.data;
							var aResultData = res.data.data;
							this.FabricSumRoll = 0;
							this.FabricSumQty = 0;
							for (var i = 0; i < aResultData.length; i++) {
								this.FabricSumRoll = this.FabricSumRoll + aResultData[i].Roll;
								this.FabricSumQty = this.FabricSumQty + aResultData[i].Qty;
							};
							this.FabricSumRoll = this.FabricSumRoll.toFixed(2) + '条';
							this.FabricSumQty = this.FabricSumQty.toFixed(2) + 'Kg';
						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.FabricGoodsDataList = [];
						} else {
							this.FabricGoodsDataList = [];
						}
					},
					fail: (error) => {
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},
		},

		GetDetailCrockNoListData: function(index) {
			console.log(this.FabricGoodsDataList[index].FabricGoodsName);
			uni.request({
				url: util.apiurl + 'rest/db/opensql',
				data: {
					token: getApp().globalData.Token,
					format: 'json',
					data: {
						db_name: getApp().globalData.AppDBName,
						sql_command_id: 'APP_GetStoreGoodsDetailCrockNoSQL',
						params: [{
							name: 'StoreName',
							value: this.StoreName
						}, {
							name: 'FGNo',
							value: this.FabricGoodsName
						}, {
							name: 'GCNO',
							value: this.GoodsCodeName
						}]
					},
				},
				success: (res) => {
					if (res.data.status == 0 && res.data.count > 0) {
						var aResultData = res.data.data;
						this.GoodsCrockNoDataList = [];
						for (var i = 0; i < aResultData.length; i++) {
							this.GoodsCrockNoDataList.push(aResultData[i]);
						};
					} else if (res.data.status == 0 && res.data.count <= 0) {
						this.GoodsCrockNoDataList = [];
					} else {
						this.GoodsCrockNoDataList = [];
					}
				},
				fail: (error) => {
					uni.showToast({
						icon: 'none',
						title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
					});
					setTimeout(function() {
						uni.hideLoading();
					}, 5000);
				},
			})
		},
	}
</script>

<style lang="scss" scoped>
	.uni-progress {
		color: red;

		::v-deep( .uni-progress-info) {
			font-size: 10px !important;
		}
	}
</style>
