<template>
	<view>
		<u-form ref="uForm">
			<view class="flex-white-plr26 ptb10 mt32 bdb_f5">
				<text class="mr26">付运凭证</text>
				<u-input disabled placeholder="自动生成" v-model="SaleShipmentOutBillNo" />
				<text class="mr26">付运日期</text> {{$u.timeFormat(SaleShipmentOutDate, 'yyyy-mm-dd')}}
			</view>
			<view @click="pickerSelectFun('仓库名称')" class="flex-white-plr26 ptb20 bdb_f5">
				<text class="mr26">仓库名称<text class="redXingh">*</text></text>
				<view :class="StoreName ? '' : 'cBlack'"> {{StoreName ? StoreName : '请选择'}}
					<u-icon class="ml26" name="arrow-right" size="40" color="#888888"></u-icon>
				</view>
			</view>
			<view @click="pickerSelectFun('司机名称')" class="flex-white-plr26 ptb20 bdb_f5">
				<text class="mr26">司机名称</text>
					<view :class="ShipCarUserName ? '' : 'cBlack'">
					{{ShipCarUserName ? ShipCarUserName : '请选择'}}
					<u-icon class="ml26" name="arrow-right" size="40" color="#888888"></u-icon>
				</view>
			</view>
			<view @click="pickerSelectFun('车牌号码')" class="flex-white-plr26 ptb20 bdb_f5">
				<text class="mr26">车牌号码</text>
					<view :class="ShipCarNo ? '' : 'cBlack'">
					{{ShipCarNo ? ShipCarNo : '请选择'}}
					<u-icon class="ml26" name="arrow-right" size="40" color="#888888"></u-icon>
				</view>
			</view>
			<view class="flex-white-plr26-column ptb20 mt32">
				<view style="margin-bottom: 8rpx;">
					<text>备注内容</text>
				</view>
				<u-input v-model="Remark" maxlength="-1" type="textarea" :border="true" :height="100"
					:auto-height="true" />
			</view>
			<u-form-item label-width="150" label="条码资料:">
				<input type="text" maxlength="-1" v-model="QRBarCode" :focus="QRBarCodeFocus"
					style="width:200px;" @confirm="SaleShipmentOutDetailScan" />
				<checkbox-group @change="BarCodeDelChange">
					<checkbox ref="checkBoxRef" :checked="BarCodeDelStatus">删除</checkbox>
				</checkbox-group>
			</u-form-item>
			<u-form-item>
				<text class="title">{{BillDataMessage}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">出仓单号：{{OutBillNo}}</text>
				<text class="title" style="width:200px;">出仓日期：{{OutBillDate}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">单据类型：{{BillTypeName}}</text>
				<text class="title" style="width:200px;">配布单号：{{SaleBillNo}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">往来单位：{{CustomerName}}</text>
				<text class="title" style="width:200px;">销 售 员：{{SaleUserName}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">付运单数：{{ShipOutBillNum}}单</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">付运单据条数：{{ShipBillSumRoll}}条</text>
				<text class="title" style="width:200px;">付运单据重量：{{ShipBillSumQty}}Kg</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">出仓单据条数：{{OutBillSumRoll}}条</text>
				<text class="title" style="width:200px;">出仓单据重量：{{OutBillSumQty}}Kg</text>
			</u-form-item>

		</u-form>
		<view class="u-demo-area">
			<u-toast ref="uToast"></u-toast>
			<wyb-table ref="table" :headers="headersMaster" :contents="GoodsDetailList" />
		</view>

		<!--提交按钮-->
		<view class="submitView">
			<u-button type="primary" style="width:100px;" class="submitBtn"
				:ripple="true" ripple-bg-color="#909399" @click="submitBtnFun">
				{{pageType ? '保存' : '提交'}}
			</u-button>

			<u-button type="primary" style="width:100px;" class="commitBtn"
				:ripple="true" ripple-bg-color="#909399" @click="commitBtnFun">
				{{commitType ? '消审' : '审核'}}
			</u-button>
		</view>
		<!--组件-->
		<!-- <u-action-sheet :list="StoreNameDataList" v-model="actionSheetShow" @click="actionSheetCallback"></u-action-sheet> -->
		<u-select v-model="selectShow" :list="selectList" @confirm="selectConfirmFun"></u-select>

	</view>
</template>

<script>
	let that = '';
	import util, {
		parFabricGoodsBarCode2D,
		parYarnGoodsBarCode2D,
		playSuccessAudio,
		playErrorAudio
	} from '../../common/util';
	import wybTable from '@/components/wyb-table/wyb-table.vue';
	export default {
		data() {
			return {
				selectShow: false,
				selectList: [],
				selectType: '',
				pageType: '',
				commitType: '',
				QRBarCodeFocus: true,
				SaleShipmentOutBillID: 0,
				SaleShipmentOutBillNo: '',
				SaleShipmentOutDate: '',
				OutBillNo: '',
				OutBillDate: '',
				SaleBillNo: '',
				BillTypeName: '',
				CustomerName: '',
				SaleUserName: '',
				StoreNameID: 0,
				StoreName: '',
				ShipCarID: 0,
				ShipCarNo: '',
				ShipCarUserID: 0,
				ShipCarUserName: '',
				actionSheetShow: false,
				BarCodeDelStatus: false,
				Remark: '',
				GoodsDetailList: [],
				StoreNameDataList: [],
				ShipCarUserDataList: [],
				ShipCarNoDataList: [],
				QRBarCode: '',
				ShipOutBillNum: 0,
				OutBillSumRoll: 0,
				OutBillSumQty: 0,
				ShipBillSumRoll: 0,
				ShipBillSumQty: 0,
				BillDataMessage: '',
				headersMaster: [{
					label: '出仓单号',
					key: 'OutBillNo'
				},{
					label: '配布单号',
					key: 'SaleBillNo'
				}, {
					label: '出仓日期',
					key: 'OutBillDate'
				}, {
					label: '往来单位',
					key: 'CustomerName'
				}, {
					label: '销售员',
					key: 'SaleUserName'
				}, {
					label: '单据类型',
					key: 'BillTypeName'
				}, {
					label: '条数',
					key: 'Roll'
				}, {
					label: '重量',
					key: 'Qty'
				}, {
					label: '仓库名称',
					key: 'StoreName'
				}],
			}
		},
		watch: {
			cpList: function(val, old) {
				that.cpCalcFun();
			}
		},
		onLoad(e) {
			that = this;

			setTimeout(() => {
				this.getStoreNameData();
			}, 100);

			setTimeout(() => {
				this.getShipCarUserData();
			}, 300);

			setTimeout(() => {
				this.getBaseDataShipCarNo();
			}, 500);

			if (getApp().globalData.StoreTypeNo.toUpperCase() == 'STOREFABRICGOODS') {
				this.StoreNameID = getApp().globalData.StoreNameID;
				this.StoreName = getApp().globalData.StoreName;
			};

			/* 		uni.$on('bjdKehuBindFun', that.bjdKehuBindFun)
					uni.$on('chanpinBindFun', that.chanpinBindFun)
					uni.$on('bjdLxrBindFun', that.bjdLxrBindFun)
					uni.$on('shangjiBindFun', that.shangjiBindFun) */
		},

		onBackPress() {
			/* 			uni.$off('bjdKehuBindFun', that.bjdKehuBindFun)
						uni.$off('chanpinBindFun', that.chanpinBindFun)
						uni.$off('bjdLxrBindFun', that.bjdLxrBindFun)
						uni.$off('shangjiBindFun', that.shangjiBindFun) */
		},
		methods: {

			playSuccess() {
				util.playSuccessAudio();
			},
			playError() {
				util.playErrorAudio();
			},

			BarCodeDelChange: function() {
				this.BarCodeDelStatus = !this.BarCodeDelStatus;
			},

			// 展示相应数据选择框
			pickerSelectFun: function(str) {
				that.selectList = [];
				if (str == '仓库名称') {
					that.selectList = this.StoreNameDataList;
				}
				else if (str == '司机名称') {
					that.selectList = this.ShipCarUserDataList;
				}else if (str == '车牌号码') {
					that.selectList = this.ShipCarNoDataList;
				}

				that.selectShow = true;
				that.selectType = str;
			},
			// 选择框选中事件
			selectConfirmFun: function(e) {
				if (that.selectType == '仓库名称') {
					that.StoreNameID = e[0].value;
					that.StoreName = e[0].label;
				} else if (that.selectType == '司机名称') {
					that.ShipCarUserID = e[0].value;
					that.ShipCarUserName = e[0].label;
				}else if (that.selectType == '车牌号码') {
					that.ShipCarID = e[0].value;
					that.ShipCarNo = e[0].label;
				}
			},

			getStoreNameData() {
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetStoreNameList',
							params: [{
								name: 'TypeNo',
								value: '%StoreFabricGoods%'
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							console.log("--->>仓库名称<<<--" + JSON.stringify(res.data));
							var aResultData = res.data.data;
							for (var i = 0; i < aResultData.length; i++) {
								this.StoreNameDataList.push({
									value: aResultData[i].StoreNameID,
									label: aResultData[i].StoreName
								});
							}
						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.StoreNameDataList = [];
						} else {
							this.StoreNameDataList = [];
						}
					},
					fail: (error) => {
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},

			getPlanDepatmentData() {
				console.log("--->>" + getApp().globalData.LoginID);
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetPlanDepartmentOwnerList',
							params: [{
								name: 'LoginID',
								value: getApp().globalData.LoginID
							}, {
								name: 'DefaultLoginID',
								value: getApp().globalData.LoginID
							}]
						},
					},

					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							var aResultData = res.data.data;
							for (var i = 0; i < aResultData.length; i++) {
								if ((aResultData[0].DefaultPlanDepartmentID != "") && (i == 0)) {
									this.PlanDepartmentID = aResultData[0].DefaultPlanDepartmentID,
										this.PlanDepartmentName = aResultData[0].DefaultPlanDepartmentName
								}
								this.PlanDepartmentDataList.push({
									value: aResultData[i].PlanDepartmentID,
									label: aResultData[i].PlanDepartmentName
								})
							}
						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.PlanDepartmentDataList = [];
						} else {
							this.PlanDepartmentDataList = [];
						}
					},
					fail: (error) => {
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},

			getShipCarUserData() {
				console.log("--->>" + getApp().globalData.LoginID);
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.BaseData.GetEmployeeList',
							params: [{
								name: 'DNo',
								value: '%ShipCarDriver%'
							}]
						},
					},

					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							console.log("--->>司机名称<<<--" + JSON.stringify(res.data));
							var aResultData = res.data.data;
							for (var i = 0; i < aResultData.length; i++) {
								this.ShipCarUserDataList.push({
									value: aResultData[i].EmployeeID,
									label: aResultData[i].EmployeeName
								})
							}
						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.ShipCarUserDataList = [];
						} else {
							this.ShipCarUserDataList = [];
						}
					},
					fail: (error) => {
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},

			getBaseDataShipCarNo() {
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.BaseData.GetBaseData',
							params: [{
								name: 'TypeNo',
								value: 'BaseData.ShipCarNo'
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							console.log("--->>车牌号码<<<--" + JSON.stringify(res.data));
							var aResultData = res.data.data;
							for (var i = 0; i < aResultData.length; i++) {
								this.ShipCarNoDataList.push({
									value: aResultData[i].BaseDataID,
									label: aResultData[i].BaseDataName
								});
							}
						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.ShipCarNoDataList = [];
						} else {
							this.ShipCarNoDataList = [];
						}
					},
					fail: (error) => {
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},

			SaleShipmentOutDetail: function() {
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetSaleShipmentOutDataDetailSQL',
							params: [{
								name: 'BillID',
								value: this.SaleShipmentOutBillID
							}]
						},
					},
					success: (res) => {
						this.triggered = false;
						let data = res.data.data;
						console.log("---->" + JSON.stringify(res.data));
						if (res.data.status == 0 && res.data.count > 0) {
							var aResultData = res.data.data;
							this.GoodsDetailList = res.data.data;
							this.ShipOutBillNum = aResultData[0].ShipOutBillNum;
							this.ShipBillSumRoll = 0;
							this.ShipBillSumQty = 0;
							for (var i = 0; i < aResultData.length; i++) {
								if (parseFloat(aResultData[i].Roll) > 0) {
									this.ShipBillSumRoll = this.ShipBillSumRoll + aResultData[i].Roll;
								}

								if (parseFloat(aResultData[i].Qty) > 0) {
									this.ShipBillSumQty = this.ShipBillSumQty + aResultData[i].Qty;
								}
							};
							this.ShipBillSumRoll = this.ShipBillSumRoll.toFixed(2);
							this.ShipBillSumQty = this.ShipBillSumQty.toFixed(2);

						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.GoodsDetailList = [];
						} else {
							this.GoodsDetailList = [];
						}

					},
				})
			},

			// 提交按钮方法
			submitBtnFun: function() {
				if (this.StoreName == '') {
					this.playError();
					this.BillDataMessage = '仓库名称不能为空，请先输入仓库名称！';
					return;
				}
				if (this.SaleShipmentOutBillID > 0) {
					this.playError();
					this.BillDataMessage = '当前单据已经提交，不能重复提交！';
					return;
				}

				uni.request({
					url: util.apiurl + 'rest/db/storedproc',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							proc_name: 'Usp_APP_SaleShipmentOutBillMaster',
							method: 'open_proc',
							params: [{
									name: '@BillMasterID',
									value: this.SaleShipmentOutBillID
								},{
									name: '@StoreNameID',
									value: this.StoreNameID
								},{
									name: '@ShipCarNo',
									value: this.ShipCarNo
								},{
									name: '@ShipCarUserID',
									value: this.ShipCarUserID
								},{
									name: '@Remark',
									value: this.Remark
								},{
									name: '@UserName',
									value: getApp().globalData.UserName
								}
							]
						},
					},
					success: (res) => {
						if (res.data.status == 0) {
							var aResultData = JSON.parse(res.data.data);
							if (aResultData.BillDataStatus == '0' && aResultData.BillDataMessage == 'SUCCESS') {
								this.playSuccess();
								this.SaleShipmentOutBillID = aResultData.SaleShipmentOutBillID;
								this.SaleShipmentOutBillNo = aResultData.SaleShipmentOutBillNo;
								this.QRBarCodeFocus = false;
								this.$nextTick(() => {
									this.QRBarCodeFocus = true;
								});
							} else {
								this.playError();
								this.BillDataMessage = '提交出错！' + aResultData.BillDataMessage;
								return;
							}
						} else {
							this.playError();
							this.BillDataMessage = '提交出错，' + res.data.msg
							return;
						}
					},
					fail: (error) => {
						this.playError();
						this.BillDataMessage = '连接服务器出错，请检查后台服务是否启动！' + res.data.msg;
					},
				})
			},

			SaleShipmentOutDetailScan() {
				if (this.StoreNameID == 0 && this.SaleShipmentOutBillNo == '') {
					this.playError();
					this.BillDataMessage = '请先新增单据';
					return;
				}

				let aBarCodeDelStatus = '0';
				if (this.BarCodeDelStatus){
					aBarCodeDelStatus = '1';
				};

				console.log("this.this.QRBarCode ---->>" + this.QRBarCode);
				uni.request({
					url: util.apiurl + 'rest/db/storedproc',
					data: {
						token: getApp().globalData.Token,
						data: {
							db_name: getApp().globalData.AppDBName,
							proc_name: 'Usp_APP_SaleShipmentOutBillDetail',
							method: 'open_proc',
							params: [{
									name: '@BillMasterID',
									value: this.SaleShipmentOutBillID
								}, {
									name: '@QRBarCode',
									value: this.QRBarCode
								}, {
									name: '@BarCodeDelStatus',
									value: aBarCodeDelStatus
								}, {
									name: '@UserName',
									value: getApp().globalData.UserName
								}
							]
						},
					},
					success: (res) => {
						if (res.data.status == 0) {
							console.log(res.data.data.toString());
							var aResultData = JSON.parse(res.data.data);
							if (aResultData.BillDataStatus == '0' && aResultData.BillDataMessage == 'SUCCESS') {
								this.playSuccess();
								this.OutBillNo = aResultData.OutBillNo;
								this.SaleBillNo = aResultData.SaleBillNo;
								this.OutBillDate = aResultData.OutBillDate;
								this.BillTypeName = aResultData.BillTypeName;
								this.CustomerName = aResultData.CustomerName;
								this.SaleUserName = aResultData.SaleUserName;
								this.OutBillSumRoll = parseFloat(aResultData.OutBillSumRoll);
								this.OutBillSumQty = parseFloat(aResultData.OutBillSumQty);
								this.ShipBillSumRoll = parseFloat(aResultData.ShipBillSumRoll);
								this.ShipBillSumQty = parseFloat(aResultData.ShipBillSumQty);
								this.QRBarCode = '';
								this.BillDataMessage = '扫描成功！';
								this.SaleShipmentOutDetail();
								this.QRBarCodeFocus = false;
								this.$nextTick(() => {
									this.QRBarCodeFocus = true;
								});
							} else {
								this.playError();
								this.BillDataMessage = '出仓出错，' + aResultData.BillDataMessage;
								this.QRBarCode = '';
								this.QRBarCodeFocus = false;
								this.$nextTick(() => {
									this.QRBarCodeFocus = true;
								});
								return;
							}

						} else {
							this.playError();
							this.QRBarCode = '';
							this.BillDataMessage = '盘点扫描出错，' + res.data.msg;
							this.QRBarCodeFocus = false;
							this.$nextTick(() => {
								this.QRBarCodeFocus = true;
							});
							return;
						}
					},
					fail: (error) => {
						this.playError();
						this.QRBarCode = '';
						this.QRBarCodeFocus = false;
						this.$nextTick(() => {
							this.QRBarCodeFocus = true;
						});
						this.BillDataMessage = '盘点扫描出错，' + res.data.msg;
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},

			addCzjlFun: function(action, _id) {
				let czjlObj = {
					create_date: new Date().getTime(),
					czRen: uni.$userInfo._id,
					pageType: 'quotation',
				}
				let xgInfoObj = that.xgInfoObj;
				czjlObj.dataId = xgInfoObj._id;
				czjlObj.type = '编辑';
				czjlObj.newUpdate = new Date().getTime();
				czjlObj.oldUpdate = xgInfoObj.update_date;
				let content = [];
				let str = '';
				if (that.bjName != xgInfoObj.bjName) {
					str = '修改 报价单名称 ，由 "' + xgInfoObj.bjName + '" 变更为 "' + that.bjName + '"'
					content.push(str)
				}
				if (that.sjName != xgInfoObj.sjName) {
					str = '修改 商机 ，由 "' + xgInfoObj.sjName + '" 变更为 "' + that.sjName + '"'
					content.push(str)
				}
				if (that.clientName != xgInfoObj.clientName) {
					str = '修改 客户 ，由 "' + xgInfoObj.clientName + '" 变更为 "' + that.clientName + '"'
					content.push(str)
				}
				if (that.bjName != xgInfoObj.bjName) {
					str = '修改 报价单名称 ，由 "' + xgInfoObj.bjName + '" 变更为 "' + that.bjName + '"'
					content.push(str)
				}
				if (that.clientLxrId != xgInfoObj.clientLxrId) {
					str = '修改 联系人 ，由 "' + xgInfoObj.clientLxr + '" 变更为 "' + that.clientLxr + '"'
					content.push(str)
				}
				if (that.bjPrice != xgInfoObj.bjPrice) {
					str = '修改 报价金额 ，由 "' + xgInfoObj.bjPrice + '" 变更为 "' + that.bjPrice + '"'
					content.push(str)
				}
				if (that.describe != xgInfoObj.describe) {
					str = '修改 备注 ，由 "' + xgInfoObj.describe + '" 变更为 "' + that.describe + '"'
					content.push(str)
				}
				if (that.bjdDate != xgInfoObj.bjdDate) {
					str = '修改 报价日期 ，由 "' + xgInfoObj.bjdDate + '" 变更为 "' + that.bjdDate + '"'
					content.push(str)
				}
				if (that.fuZeRen != xgInfoObj.fuZeRen) {
					str = '修改 负责人 ，由 "' + xgInfoObj.fuZeRen + '" 变更为 "' + that.fuZeRen + '"'
					content.push(str)
				}
				czjlObj.content = content;
				uni.$czjlApiAddFun(czjlObj);
			},
			// 数据恢复
			setDataFun: function() {
				let obj = uni.$infoObj
				that.xgInfoObj = uni.$infoObj
				that.bjName = obj.bjName
				that.sjName = obj.sjName
				that.sjId = obj.sjId
				that.clientName = obj.clientName
				that.clientId = obj.clientId
				that.clientLxr = obj.clientLxr
				that.clientLxrId = obj.clientLxrId
				that.bjPrice = obj.bjPrice
				that.describe = obj.describe
				that.bjdDate = obj.bjdDate
				that.bjdDateTime = obj.bjdDateTime
				// that.cpList = obj.cpList
				// that.cpHzObj = obj.cpHzObj || {}
				that.fuZeRenId = obj.fuZeRenId
				that.fuZeRen = obj.fuZeRen
				that.getCpByIdFun()
			},
			getCpByIdFun: function() {
				let reqData = {
					action: 'getCp',
					params: {
						bjdId: that.xgInfoObj._id
					}
				}
				uni.showLoading({
					title: '加载中...'
				})
				crmBaoJiaDanApi(reqData)
					.then(res => {
						let data = res.result.data;
						if (data.length > 0) {
							let cpHzObj = {
								discount: 0,
								price: 0,
								sumPrice: 0,
								totalNum: 0
							};
							for (var i = 0; i < data.length; i++) {
								var obj = data[i];
								obj.discount = (obj.sellingPrice / obj.price * 100).toFixed(2);
								obj.totalPrice = (obj.sellingPrice * obj.num).toFixed(2);
								data[i] = obj;
								cpHzObj.price += parseFloat(obj.price) * obj.num;
								cpHzObj.sumPrice += parseFloat(obj.sellingPrice) * obj.num;
								cpHzObj.totalNum += obj.num;
							}
							cpHzObj.discount = cpHzObj.sumPrice / cpHzObj.price * 100;
							that.cpHzObj = cpHzObj;
							that.cpList = data;
						}
					})
			},
			// 根据商机id查询产品
			getCpBysjIdFun: function(sjId) {
				let reqData = {
					action: 'getCp',
					params: {
						sjId: sjId
					}
				}
				uni.showLoading({
					title: '加载中...'
				})
				crmShangJiApi(reqData)
					.then(res => {
						let data = res.result.data;
						if (data.length > 0) {
							let cpHzObj = {
								discount: 0,
								price: 0,
								sumPrice: 0,
								totalNum: 0
							};
							for (var i = 0; i < data.length; i++) {
								var obj = data[i];
								obj._id = '';
								obj.discount = (obj.sellingPrice / obj.price * 100).toFixed(2);
								obj.totalPrice = (obj.sellingPrice * obj.num).toFixed(2);
								data[i] = obj;
								cpHzObj.price += parseFloat(obj.price) * obj.num;
								cpHzObj.sumPrice += parseFloat(obj.sellingPrice) * obj.num;
								cpHzObj.totalNum += obj.num;
							}
							cpHzObj.discount = cpHzObj.sumPrice / cpHzObj.price * 100;
							that.cpHzObj = cpHzObj;
							that.cpList = data;
						}
					})
			},
		}
	}
</script>

<style>
	page {
		background-color: #F8F8F8;
		padding-bottom: 260rpx;
	}

	.u-radio {
		width: 200rpx !important;
	}

	.submitView {
		width: 100%;
		padding: 16rpx 0 26rpx;
		background-color: #FFFFFF;
		position: fixed;
		bottom: 0;
		left: 0;
		border-top: 1rpx solid #f1f1f1;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 100;
	}

	.submitBtn {
		width: 666rpx;
	}

	.productBox {
		background-color: #FFFFFF;
		margin-top: 32rpx;
		padding: 26rpx 0;
	}

	.tjcpName {
		width: 686rpx;
		height: 40rpx;
		font-size: 16px;
		font-weight: bold;
		border-left: 6rpx solid #007AFF;
		padding-left: 12rpx;
		margin-left: 26rpx;
		margin-top: 26rpx;
	}

	.cpInput {
		width: 150rpx !important;
		margin-right: 12rpx;
	}

	.cpInput>input {
		box-sizing: border-box;
		border: 1rpx solid #DDDDDD;
		width: 100%;
		height: 60rpx;
		border-radius: 10rpx;
		padding: 0 10rpx;
	}

	.cpInput1 {
		width: 200rpx !important;
		margin-right: 12rpx;
	}

	.cpInput1>input {
		box-sizing: border-box;
		border: 1rpx solid #DDDDDD;
		width: 100%;
		height: 60rpx;
		border-radius: 10rpx;
		padding: 0 10rpx;
	}

	.clearIcon {
		position: absolute;
		right: 6rpx;
		top: 6rpx;
	}

	.greenPrice {
		font-size: 16px;
		color: #19BE6B !important;
		font-weight: bold;
	}

	.disFlex {
		display: flex;
		align-items: center;
		margin-bottom: 8rpx;
	}

	.inputName {
		color: #ADADAD;
		font-size: 16px;
	}

	.addHKQS {
		display: flex;
		align-items: center;
		padding: 16rpx 26rpx;
		font-size: 15px;
		font-weight: bold;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>
