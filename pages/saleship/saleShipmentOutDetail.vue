<template>
	<view class="wrap">
		<u-form ref="uForm">
			<u-form-item>
				<text class="title" style="width:200px;">付运单号：{{SaleShipmentOutBillNo}}</text>
				<text class="title" style="width:200px;">付运日期：{{SaleShipmentOutDate}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">仓库名称：{{StoreName}}</text>
				<text class="title" style="width:200px;">出车时间：{{ShipOutTime}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">司机名称：{{ShipCarUserName}}</text>
				<text class="title" style="width:200px;">车牌号码：{{ShipCarNo}}</text>
			</u-form-item>
			<u-form-item label-width="150" label="条码资料:">
				<input type="text" maxlength="-1" v-model="QRBarCode" :focus="QRBarCodeFocus"
					style="width:200px;" @confirm="SaleShipmentOutDetailScan" />
				<checkbox-group @change="BarCodeDelChange">
					<checkbox ref="checkBoxRef" :checked="BarCodeDelStatus">删除</checkbox>
				</checkbox-group>
			</u-form-item>
			<u-form-item>
				<text class="title">{{BillDataMessage}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">出仓单号：{{OutBillNo}}</text>
				<text class="title" style="width:200px;">出仓日期：{{OutBillDate}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">单据类型：{{BillTypeName}}</text>
				<text class="title" style="width:200px;">配布单号：{{SaleBillNo}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">往来单位：{{CustomerName}}</text>
				<text class="title" style="width:200px;">销 售 员：{{SaleUserName}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">付运单数：{{ShipOutBillNum}}单</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">付运单据条数：{{ShipBillSumRoll}}条</text>
				<text class="title" style="width:200px;">付运单据重量：{{ShipBillSumQty}}Kg</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">出仓单据条数：{{OutBillSumRoll}}条</text>
				<text class="title" style="width:200px;">出仓单据重量：{{OutBillSumQty}}Kg</text>
			</u-form-item>

		</u-form>
		<view class="u-demo-area">
			<u-toast ref="uToast"></u-toast>
			<wyb-table ref="table" :headers="headersMaster" :contents="GoodsDetailList"/>
			<!--	<wyb-table ref="table" :headers="headersDetail" :contents="GoodsCrockNoDataList"  height="600rpx" /> !-->
		</view>
		<!-- 		<u-action-sheet :list="InDetailList" v-model="actionSheetShow"  @click="actionSheetCallback"></u-action-sheet> -->
	</view>
</template>

<script>
	import util, {
		parFabricGoodsBarCode2D,
		parYarnGoodsBarCode2D,
		playSuccessAudio,
		playErrorAudio
	} from '../../common/util';
	import wybTable from '@/components/wyb-table/wyb-table.vue';
	let that = '';
	export default {
		data() {
			return {
				BillMasterID: 0,
				borderColor: '#e4e7ed',
				align: 'center',
				index: 0,
				actionSheetShow: false,
				commitType: '',
				QRBarCodeFocus: true,
				SaleShipmentOutBillID: 0,
				SaleShipmentOutBillNo: '',
				SaleShipmentOutDate: '',
				ShipOutTime: '',
				OutBillNo: '',
				OutBillDate: '',
				SaleBillNo: '',
				BillTypeName: '',
				CustomerName: '',
				SaleUserName: '',
				StoreNameID: 0,
				StoreName: '',
				ShipCarID: 0,
				ShipCarNo: '',
				ShipCarUserID: 0,
				ShipCarUserName: '',
				actionSheetShow: false,
				BarCodeDelStatus: false,
				Remark: '',
				GoodsDetailList: [],
				StoreNameDataList: [],
				ShipCarUserDataList: [],
				ShipCarNoDataList: [],
				QRBarCode: '',
				ShipOutBillNum: 0,
				OutBillSumRoll: 0,
				OutBillSumQty: 0,
				ShipBillSumRoll: 0,
				ShipBillSumQty: 0,
				BillDataMessage: '',
				headersMaster: [{
					label: '出仓单号',
					key: 'OutBillNo'
				}, {
					label: '配布单号',
					key: 'SaleBillNo'
				}, {
					label: '出仓日期',
					key: 'OutBillDate'
				}, {
					label: '往来单位',
					key: 'CustomerName'
				},  {
					label: '销售员',
					key: 'SaleUserName'
				}, {
					label: '单据类型',
					key: 'BillTypeName'
				}, {
					label: '条数',
					key: 'Roll'
				}, {
					label: '重量',
					key: 'Qty'
				}, {
					label: '仓库名称',
					key: 'StoreName'
				}],
			}
		},

		onLoad(e) {
			that = this;
			if (e.billid) {
				that.SaleShipmentOutBillID = e.billid;
			}
			console.log("---BillID->" + this.SaleShipmentOutBillID);
			this.SaleShipmentOutDetail();
		},

		methods: {
			playSuccess() {
				util.playSuccessAudio();
			},
			playError() {
				util.playErrorAudio();
			},

			SaleShipmentOutDetail: function() {
				console.log("----SaleShipmentOutBillID--->>>>" + this.SaleShipmentOutBillID)
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetSaleShipmentOutDataDetailSQL',
							params: [{
								name: 'BillID',
								value: this.SaleShipmentOutBillID
							}]
						},
					},
					success: (res) => {
						this.triggered = false;
						let data = res.data.data;
						console.log("---->" + JSON.stringify(res.data));
						if (res.data.status == 0 && res.data.count > 0) {
							var aResultData = res.data.data;
							this.GoodsDetailList = res.data.data;
							this.SaleShipmentOutBillNo = aResultData[0].SaleShipmentOutBillNo;
							this.SaleShipmentOutDate = aResultData[0].SaleShipmentOutDate;
							this.StoreName = aResultData[0].MasterStoreName;
							this.ShipOutTime = aResultData[0].ShipOutTime;
							this.ShipCarNo = aResultData[0].ShipCarNo;
							this.ShipCarUserName = aResultData[0].ShipCarUserName;
							this.ShipOutBillNum = aResultData[0].ShipOutBillNum;
							this.ShipBillSumRoll = 0;
							this.ShipBillSumQty = 0;
							for (var i = 0; i < aResultData.length; i++) {
								if (parseFloat(aResultData[i].Roll) > 0) {
									this.ShipBillSumRoll = this.ShipBillSumRoll + aResultData[i].Roll;
								}

								if (parseFloat(aResultData[i].Qty) > 0) {
									this.ShipBillSumQty = this.ShipBillSumQty + aResultData[i].Qty;
								}
							};
							this.ShipBillSumRoll = this.ShipBillSumRoll.toFixed(2);
							this.ShipBillSumQty = this.ShipBillSumQty.toFixed(2);

						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.GoodsDetailList = [];
						} else {
							this.GoodsDetailList = [];
						}
					},
				})
			},

			SaleShipmentOutDetailScan() {
				if (this.StoreNameID == 0 && this.SaleShipmentOutBillNo == '') {
					this.playError();
					this.BillDataMessage = '请先新增单据';
					return;
				}

				let aBarCodeDelStatus = '0';
				if (this.BarCodeDelStatus){
					aBarCodeDelStatus = '1';
				};

				console.log("this.this.QRBarCode ---->>" + this.QRBarCode);
				uni.request({
					url: util.apiurl + 'rest/db/storedproc',
					data: {
						token: getApp().globalData.Token,
						data: {
							db_name: getApp().globalData.AppDBName,
							proc_name: 'Usp_APP_SaleShipmentOutBillDetail',
							method: 'open_proc',
							params: [{
									name: '@BillMasterID',
									value: this.SaleShipmentOutBillID
								}, {
									name: '@QRBarCode',
									value: this.QRBarCode
								}, {
									name: '@BarCodeDelStatus',
									value: aBarCodeDelStatus
								}, {
									name: '@UserName',
									value: getApp().globalData.UserName
								}
							]
						},
					},
					success: (res) => {
						if (res.data.status == 0) {
							console.log(res.data.data.toString());
							var aResultData = JSON.parse(res.data.data);
							if (aResultData.BillDataStatus == '0' && aResultData.BillDataMessage == 'SUCCESS') {
								this.playSuccess();
								this.OutBillNo = aResultData.OutBillNo;
								this.SaleBillNo = aResultData.SaleBillNo;
								this.OutBillDate = aResultData.OutBillDate;
								this.BillTypeName = aResultData.BillTypeName;
								this.CustomerName = aResultData.CustomerName;
								this.SaleUserName = aResultData.SaleUserName;
								this.OutBillSumRoll = parseFloat(aResultData.OutBillSumRoll);
								this.OutBillSumQty = parseFloat(aResultData.OutBillSumQty);
								this.ShipBillSumRoll = parseFloat(aResultData.ShipBillSumRoll);
								this.ShipBillSumQty = parseFloat(aResultData.ShipBillSumQty);
								this.QRBarCode = '';
								this.BillDataMessage = '扫描成功！';
								this.SaleShipmentOutDetail();
								this.QRBarCodeFocus = false;
								this.$nextTick(() => {
									this.QRBarCodeFocus = true;
								});
							} else {
								this.playError();
								this.BillDataMessage = '出仓出错，' + aResultData.BillDataMessage;
								this.QRBarCode = '';
								this.QRBarCodeFocus = false;
								this.$nextTick(() => {
									this.QRBarCodeFocus = true;
								});
								return;
							}

						} else {
							this.playError();
							this.QRBarCode = '';
							this.BillDataMessage = '盘点扫描出错，' + res.data.msg;
							this.QRBarCodeFocus = false;
							this.$nextTick(() => {
								this.QRBarCodeFocus = true;
							});
							return;
						}
					},
					fail: (error) => {
						this.playError();
						this.QRBarCode = '';
						this.QRBarCodeFocus = false;
						this.$nextTick(() => {
							this.QRBarCodeFocus = true;
						});
						this.BillDataMessage = '盘点扫描出错，' + res.data.msg;
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},
		}
	}
</script>

<style>
	page {
		background-color: #F8F8F8;
		padding-bottom: 260rpx;
	}

	.u-radio {
		width: 200rpx !important;
	}

	.submitView {
		width: 100%;
		padding: 16rpx 0 26rpx;
		background-color: #FFFFFF;
		position: fixed;
		bottom: 0;
		left: 0;
		border-top: 1rpx solid #f1f1f1;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 100;
	}

	.submitBtn {
		width: 666rpx;
	}

	.productBox {
		background-color: #FFFFFF;
		margin-top: 32rpx;
		padding: 26rpx 0;
	}

	.tjcpName {
		width: 686rpx;
		height: 40rpx;
		font-size: 16px;
		font-weight: bold;
		border-left: 6rpx solid #007AFF;
		padding-left: 12rpx;
		margin-left: 26rpx;
		margin-top: 26rpx;
	}

	.cpInput {
		width: 150rpx !important;
		margin-right: 12rpx;
	}

	.cpInput>input {
		box-sizing: border-box;
		border: 1rpx solid #DDDDDD;
		width: 100%;
		height: 60rpx;
		border-radius: 10rpx;
		padding: 0 10rpx;
	}

	.cpInput1 {
		width: 200rpx !important;
		margin-right: 12rpx;
	}

	.cpInput1>input {
		box-sizing: border-box;
		border: 1rpx solid #DDDDDD;
		width: 100%;
		height: 60rpx;
		border-radius: 10rpx;
		padding: 0 10rpx;
	}

	.clearIcon {
		position: absolute;
		right: 6rpx;
		top: 6rpx;
	}

	.greenPrice {
		font-size: 16px;
		color: #19BE6B !important;
		font-weight: bold;
	}

	.disFlex {
		display: flex;
		align-items: center;
		margin-bottom: 8rpx;
	}

	.inputName {
		color: #ADADAD;
		font-size: 16px;
	}

	.addHKQS {
		display: flex;
		align-items: center;
		padding: 16rpx 26rpx;
		font-size: 15px;
		font-weight: bold;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>
