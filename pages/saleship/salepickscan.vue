<template>
  <view>
    <!-- 标签页 -->
    <u-sticky>
      <div
        style="
          height: 43px;
          border-bottom: 1rpx solid #eeeeee;
          background-color: #ffffff;
        "
      >
        <u-tabs
          :list="tabList"
          name="title"
          active-color="red"
          :is-scroll="false"
          :current="activeTabIndex"
          @change="onTabChange"
        ></u-tabs>
      </div>
    </u-sticky>

    <!-- 搜索框 -->
    <view class="search-box">
      <u-search
        v-model="searchKeyword"
        placeholder="请输入配布单号"
        :show-action="false"
        @change="onSearch"
        @custom="onSearch"
        @search="onSearch"
      ></u-search>
    </view>

    <!-- 内容展示区 -->
    <view class="content">
      <!-- 空状态展示 -->
      <view v-if="showEmpty" class="empty-state">
        <dataNull
          src="/static/img/chahua/gjNull.png"
          title="暂无相关配布单"
          title1="请添加或者更换搜索添加"
        ></dataNull>
      </view>

      <!-- 列表展示 -->
      <scroll-view
        v-else
        scroll-y="true"
        :style="{ height: 'calc(100vh - 120px)' }"
        @scrolltolower="onLoadMore"
        refresher-enabled
        :refresher-triggered="triggered"
        @refresherrefresh="onRefresh"
        @refresherrestore="onRestore"
        @refresherpulling="onPulling"
        :refresher-threshold="45"
        refresher-default-style="black"
        :refresher-background="'#f8f8f8'"
      >
        <view
          v-for="(item, index) in list"
          :key="index"
          @click="cardClickFun(item, index)"
        >
          <salepickscanview
            :item="item"
            :isSelect="isSelect"
            :index="index"
          ></salepickscanview>
        </view>

        <!-- 加载更多提示 -->
        <view class="load-more" v-if="list.length > 0">
          {{ hasMore ? "正在加载更多..." : "没有更多数据了" }}
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
import dataNull from "@/components/dataNull/dataNull.vue";
import salepickscanview from "@/pages/saleship/salepickscanview.vue";
import util from "../../common/util";

export default {
  components: {
    dataNull,
    salepickscanview,
  },

  data() {
    return {
      isSelect: false,
      // tab列表
      tabList: [
        { title: '未配', status: '1,2' },    // 未配：1待配布,2配布中
        { title: '已配', status: '3,4' },    // 已配：3已配布,4待出仓
        { title: '全部', status: '1,2,3,4' } // 全部：所有状态
      ],
      // 当前激活的tab索引
      activeTabIndex: 0,
      // 搜索关键词
      searchKeyword: "",
      // 列表数据
      list: [],
      // 加载状态
      loading: false,
      pageIndex: 1,
      pageSize: 15,
      hasMore: true,
      triggered: false,
      isRefreshing: false,
      searchTimer: null, // 添加防抖定时器
    };
  },

  computed: {
    showEmpty() {
      return !this.loading && this.list.length === 0;
    },
  },

  methods: {
    // 获取数据
    async getData(isLoadMore = false) {
      this.$u.api.getFpmArrangeOrderList({
        order_no: this.searchKeyword,
        src_order_no: this.searchKeyword,
        business_status_ids: this.tabList[this.activeTabIndex].status, // 根据tab传递对应的状态组合
        page: this.pageIndex,
        size: this.pageSize,
      }).then(res => {
        const newList = res.list || [];
        this.hasMore = newList.length === this.pageSize;
        this.list = isLoadMore ? [...this.list, ...newList] : newList;
        console.log('newList',newList)
      }).catch(e => {
        uni.showToast({
          title: e.msg,
          icon: "none",
        });
      }).finally(() => {
        this.loading = false;
        uni.hideLoading();
        if (!isLoadMore) {
          setTimeout(() => {
            this.triggered = false;
            this.isRefreshing = false;
          }, 500);
        }
      })
    },

    // 下拉刷新
    async onRefresh() {
      if (this.isRefreshing) return;

      this.isRefreshing = true;
      this.triggered = true;
      this.pageIndex = 1;
      this.hasMore = true;

      try {
        await this.getData(false);
      } catch (error) {
        uni.showToast({
          title: "刷新失败",
          icon: "none",
        });
      }
    },

    // 加载更多
    onLoadMore() {
      if (this.loading || !this.hasMore) return;
      this.pageIndex += 1;
      this.loadData(true);
    },

    // tab切换
    onTabChange(index) {
      this.activeTabIndex = index;
      this.searchKeyword = "";
      this.pageIndex = 1;
      this.hasMore = true;
      this.list = [];
      this.loadData();
    },

    // 搜索防抖处理
    onSearch() {
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }

      this.searchTimer = setTimeout(() => {
        this.pageIndex = 1;
        this.hasMore = true;
        this.list = [];
        this.loadData();
      }, 300); // 300ms 的防抖延迟
    },

    // 加载数据
    loadData(isLoadMore = false) {
      if (this.loading) return;

      this.loading = true;
      if (!isLoadMore) {
        uni.showLoading({
          title: "加载中...",
          mask: false,
        });
      }

      this.getData(isLoadMore);
    },

    // 点击卡片
    cardClickFun(item) {
      uni.navigateTo({
        url: `/pages/saleship/salepickscandetail?billid=${item.id}&order_no=${item.order_no}`,
      });
    },

    onPulling() {},
    onRestore() {
      this.triggered = false;
      this.isRefreshing = false;
    },
  },

  onLoad() {
    this.loadData();
  },

  // 组件销毁时清理定时器
  onUnload() {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
  }
};
</script>

<style>
page {
  background-color: #f8f8f8;
}

.search-box {
  padding: 10px;
  background-color: #ffffff;
}

.content {
  padding: 5px 0px 10px 0px;
  min-height: calc(100vh - 120px);
}

.content text {
  display: block;
  margin-bottom: 10px;
  font-size: 14px;
  color: #666;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
}

.load-more {
  text-align: center;
  padding: 15px 0;
  color: #999;
  font-size: 14px;
}
</style>
