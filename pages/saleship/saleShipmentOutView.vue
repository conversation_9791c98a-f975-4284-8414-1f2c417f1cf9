<template>
	<view class="myCard" @click="gotoDetailFun">
		<view class="cardTopName">付运单号：{{item.BillNo}}</view>
		<view class="cardRow">
			<view>付运日期：</view>
			<view>{{item.BillDate}}</view>
		</view>
		<view class="cardRow">
			<view>营销部门：</view>
			<view>{{item.PlanDepartmentName}}</view>
		</view>
		<view class="cardRow">
			<view>送货司机：</view>
			<view>{{item.ShipCarUserName}}</view>
		</view>
		<view class="cardRow">
			<view>车牌号码：</view>
			<view>{{item.ShipCarNo}}</view>
		</view>
		<view class="cardRow">
			<view>付运单数：</view>
			<text class="fzrRight">{{item.ShipOutBillNum}}单</text>
		</view>
		<view class="cardRow">
			<view>付运数量：</view>
			<text class="fzrRight">{{item.ShipOutRoll}}条 {{item.ShipOutQty}}KG</text>
		</view>
		<view class="cardRow">
			<view>创 建 人：</view>
			<view>{{item.OpName}} {{item.OpDate}}</view>
		</view>
		<view class="cardRow">
			<view>审 核 人：</view>
			<view>{{item.CommitName}} {{item.CommitDate}}</view>
		</view>
		<view v-if="!isDetail && !isSelect" class="lookDetail">
			<text>查看详情</text>
			<u-icon name="arrow-right" size="36"></u-icon>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			item: {
				type: Object,
				default: () => {}
			},
			index: {
				type: Number,
				default: 0
			},
			isSelect: {
				type: Boolean,
				default: false
			},
			isDetail: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				
			}
		},
		methods: {
			gotoDetailFun: function() {
				if(this.isSelect) {
					return
				}
				//uni.$bjInfo = this.item;
				console.log("----->>>>" + this.index);
				uni.navigateTo({
					url: '/pages/saleship/detail?index=' + this.index
				})
			}
		}
	}
</script>

<style>
	
</style>

