<template>
	<view>
		<view class="flex-white-plr26 ptb10 mt32 bdb_f5">
			<text class="mr26">预约单号</text>
			<u-input disabled placeholder="自动生成" v-model="AllocatedBillNo" />
			<text class="mr26">预约日期</text> {{$u.timeFormat(AllocatedDate, 'yyyy-mm-dd')}}
		</view>
		<view class="flex-white-plr26 ptb20 bdb_f5" @click="selectCustomer">
			<text class="mr26">客户名称<text class="redXingh">*</text></text>
			<view :class="CustomerName ? '' : 'cBlack'">
				{{CustomerName ? CustomerName : '请选择'}}
				<u-icon class="ml26" name="arrow-right" size="40" color="#888888"></u-icon>
			</view>
		</view>
		<view class="flex-white-plr26 ptb20 bdb_f5" @click="selectShangJiFun">
			<text class="mr26">营销部门<text class="redXingh">*</text></text>
			<view :class="PlanDepartmentName ? '' : 'cBlack'">
				{{PlanDepartmentName ? PlanDepartmentName : '请选择'}}
				<u-icon class="ml26" name="arrow-right" size="40" color="#888888"></u-icon>
			</view>
		</view>
		<view class="flex-white-plr26 ptb20 bdb_f5" @click="selectShangJiFun">
			<text class="mr26">销 售 员<text class="redXingh">*</text></text>
			<view :class="SaleUserName ? '' : 'cBlack'">
				{{SaleUserName ? SaleUserName : '请选择'}}
				<u-icon class="ml26" name="arrow-right" size="40" color="#888888"></u-icon>
			</view>
		</view>
		<view @click="pickerSelectFun('结算方式')" class="flex-white-plr26 ptb20 bdb_f5">
			<text class="mr26">结算方式<text class="redXingh">*</text></text>
			<view :class="PaymentTypeName ? '' : 'cBlack'">
				{{PaymentTypeName ? PaymentTypeName : '请选择'}}
				<u-icon class="ml26" name="arrow-right" size="40" color="#888888"></u-icon>
			</view>
		</view>
		<view class="flex-white-plr26 ptb10 bdb_f5">
			<text class="mr26">物流公司<text class="redXingh">*</text></text>
			<u-input v-model="ShipCompanyName" />
		</view>
		<view class="flex-white-plr26 ptb10 bdb_f5">
			<text class="mr26">收货地址<text class="redXingh">*</text></text>
			<u-input v-model="SaleCustomerAddress" />
		</view>
		<view class="flex-white-plr26-column ptb20 mt32">
			<view style="margin-bottom: 8rpx;">
				<text>内部备注</text>
			</view>
			<u-input v-model="Remark" type="textarea" :border="true" :height="100" :auto-height="true" />
		</view>
		<view class="flex-white-plr26-column ptb20 mt32">
			<view style="margin-bottom: 8rpx;">
				<text>出货备注</text>
			</view>
			<u-input v-model="SaleOutRemark" type="textarea" :border="true" :height="100" :auto-height="true" />
		</view>
		<!--添加产品-->
		<view class="tjcpName">
			<text>添加产品</text>
		</view>
		<view class="productBox">
			<view class="myCard" v-if="cpList.length > 0">
				<view class="cardRow">
					<view>总数量：</view>
					<view>{{cpHzObj.totalNum}}</view>
				</view>
				<view class="cardRow">
					<view>价格合计：</view>
					<view>{{cpHzObj.price}}元</view>
				</view>
				<view class="cardRow">
					<view>总折扣%：</view>
					<view class="cpInput1">
						<u-input v-model="cpHzObj.discount" type="number" height="50" :border="true" :clearable="false" placeholder="请输入"  @input="cpzzkXgFun"/>
					</view>
					
				</view>
				<view class="cardRow">
					<view>最终价格：</view>
					<view class="cpInput1">
						<u-input v-model="cpHzObj.sumPrice" type="number" height="50" :border="true" :clearable="false" placeholder="请输入"  @input="cpzzPriceXgFun"/>
					</view>
				</view>
			</view>
			<view class="myCard" v-for="(item, index) in cpList" :key="index">
				<u-icon name="close-circle-fill" size="50" class="clearIcon" color="#ff0000" @click="deleteCpFun(index)"></u-icon>
				<view class="cardTopName">产品名称：{{item.pName}}</view>
				<view class="cardRow">
					<view>产品价格：</view>
					<view>{{item.price}}元/{{item.unit}}</view>
				</view>
				<view class="disFlex">
					<view class="inputName">售价 (元)：</view>
					<view class="cpInput">
						<u-input v-model="item.sellingPrice" type="number" height="50" :border="true" :clearable="false" placeholder="请输入"  @input="cpPriceUpdateFun(index, item)"/>
					</view>
					<view class="inputName">折扣 (%)：</view>
					<view class="cpInput">
						<u-input v-model="item.discount" type="number" height="50" :border="true" :clearable="false" placeholder="请输入" @input="cpDiscountUpdateFun(index, item)"/>
					</view>
				</view>
				<view class="cardRow">
					<view>产品数量：</view>
					<view>
						<u-number-box v-model="item.num" :index="index" @change="cpCalcFun" :input-width="128" :input-height="60" :min="1" :max="100000"></u-number-box>
					</view>
				</view>
				<view class="cardRow">
					<view>价格合计：</view>
					<view class="greenPrice">￥{{item.totalPrice}}</view>
				</view>
			</view>
			<view class="addHKQS" @click="addChanPinFun">
				<text>点击添加相关产品</text>
				<u-icon name="plus-circle-fill" color="#007aff" size="56"
					style="margin-left: 16rpx;">
				</u-icon>
			</view>
		</view>
		<!--提交按钮-->
		<view class="submitView">
			<u-button type="primary" class="submitBtn" :ripple="true" ripple-bg-color="#909399" @click="submitBtnFun">{{pageType ? '保存' : '提交'}}</u-button>
		</view>
		<!--组件-->
		
		<u-select v-model="selectShow" :list="selectList" @confirm="selectConfirmFun"></u-select>
		
	</view>
</template>

<script>
	let that = '';
	import { getNowDate, getDayFun } from '../../static/utils/date.js'
	import { crmBaoJiaDanApi, crmShangJiApi } from '../../static/utils/api.js'
	export default {
		data() {
			return {
				selectShow: false,
				selectList: [],
				selectType: '',
				PaymentTypeNameList: [{text: '现款'},{text: '帐期'}],
				AllocatedBillNo: '',
				AllocatedDate:'',
				CustomerID: '',
				CustomerName: '',	
				PlanDepartmentID: '',
				PlanDepartmentName: '',
				SaleUserID: '',
				SaleUserName: '',
				PaymentTypeName: '',
				ShipCompanyName: '',
				SaleCustomerAddress: '',
				Remark: '',
				SaleOutRemark: '',
				sjLaiYuan: '',
				describe: '',
				bjdDate: '',
				bjdDateTime: '',
				cpList: [],
				cpHzObj: {
					totalNum: 0,
					price: 0,
					discount: 0,
					sumPrice: 0
				},
				isTjcp: false,
				isGlsj: false,
				fuZeRenId: getApp().globalData.EmployeeID,
				fuZeRen: getApp().globalData.UserName,
				pageType: '',
				xgInfoObj: {}
			}
		},
		watch: {
			cpList: function(val, old) {
				that.cpCalcFun();
			}
		},
		onLoad(e) {
			that = this;
			if(e.type == 'update') {
				that.setDataFun();
				that.pageType = 'update';
				that.navbarTitle = '修改报价单信息';
			}
			if(e.clientName) {
				that.clientName = e.clientName;
			}
			if(e.clientId) {
				that.clientId = e.clientId;
			}
			uni.$on('bjdKehuBindFun', that.bjdKehuBindFun)
			uni.$on('chanpinBindFun', that.chanpinBindFun)
			uni.$on('bjdLxrBindFun', that.bjdLxrBindFun)
			uni.$on('shangjiBindFun', that.shangjiBindFun)
		},
		
		onBackPress() {
			uni.$off('bjdKehuBindFun', that.bjdKehuBindFun)
			uni.$off('chanpinBindFun', that.chanpinBindFun)
			uni.$off('bjdLxrBindFun', that.bjdLxrBindFun)
			uni.$off('shangjiBindFun', that.shangjiBindFun)
		},
		methods: {
			// 日期修改
			bindDateChange: function(e) {
				that.bjdDate = e.detail.value;
				that.bjdDateTime = new Date(e.detail.value + ' 00:00:00').getTime()
			},
			// 展示相应数据选择框
			pickerSelectFun: function(str) {
				if(str == '结算方式') {
					that.selectList = [
						{ value: 0, label: '现款'},
						{ value: 1, label: '账期'}
					]
				}
				that.selectShow = true;
				that.selectType = str;
			},
			// 选择框选中事件
			selectConfirmFun: function(e) {
				if(that.selectType == '结算方式') {
					that.PaymentTypeName = e[0].label;
				} 
			},
			
			scanFun: function () {
				uni.scanCode({
					success(res) {
						that.code = res.result;
					}
				})
			},
			// 选择所属客户
			selectCustomer: function() {
				uni.navigateTo({
					url: '../basedata/customer/customer?type=saleallocated'
				})
			},
			// 绑定客户
			bjdKehuBindFun: function(e) {
				that.CustomerID = e.CustomerID;
				that.CustomerName = e.CustomerName;
				that.PlanDepartmentID = e.PlanDepartmentID;
				that.PlanDepartmentName = e.PlanDepartmentName;
				that.SaleUserID = e.SaleUserID;
				that.SaleUserName = e.SaleUserName;
				that.SaleCustomerAddress = e.CustomerAddress+' '+e.CustomerPhone+' '+e.CustomerLinkName;
				/*
				that.clientId = e.clientId;
				that.clientName = e.clientName;
				*/
			},
			// 选择客户联系人
			selectLxrFun: function() {
				if(!that.clientId) {
					uni.$myModalFun('请先关联客户！')
					return
				}
				uni.navigateTo({
					url: '../lianxiren/lianxiren?type=bjd&clientId=' + that.clientId
				})
			},
			// 绑定联系人
			bjdLxrBindFun: function(e) {
				that.clientLxrId = e.lxrId;
				that.clientLxr = e.lxrName;
			},
			// 选择商机
			selectShangJiFun: function() {
				if(!that.clientId) {
					uni.$myModalFun('请先关联客户！')
					return
				}
				that.isGlsj = true;
				uni.navigateTo({
					url: '../shangji/shangji?type=bjd&clientId=' + that.clientId
				})
			},
			// 绑定商机
			shangjiBindFun: function(e) {
				that.sjId = e._id;
				that.sjName = e.sjName;
				that.clientId = e.clientId;
				that.clientName = e.clientName;
				that.clientLxrId = e.clientLxrId;
				that.clientLxr = e.clientLxr;
				if(that.isGlsj) {
					setTimeout(()=> {
						if(e.cpNum) {
							uni.showModal({
								title: '提示',
								content: '该商机下有' + e.cpNum + '种产品信息，是否关联或替换至该报价单中？',
								success(res) {
									if(res.confirm) {
										that.getCpBysjIdFun(e._id)
									}
								}
							})
						}
					}, 8)
					that.isGlsj = false;
				}
			},
			// 添加相关产品
			addChanPinFun: function() {
				if (that.CustomerName == "")
				{
					uni.showToast({
					        title: '客户名称不能为空，请先选择！',
					        duration: 2000
					    });
					return
				}
				console.log("--->>" + that.CustomerID +'<<<<');
				uni.navigateTo({
					url: '../../chanpin/view/shop?customerid=' + that.CustomerID
					//url: '../../chanpin/view/shop?type=baojiadan'
				})
				that.isTjcp = true;
			},
			// 绑定产品
			
			
			chanpinBindFun: function(e) {
				let obj = {
					pName: e.pName,
					price: e.price,
					unit: e.unit,
					cpId: e._id,
					sellingPrice: e.price,
					discount: 100,
					num: 1,
					totalPrice: e.price
				}
				if(that.isTjcp) {
					let arr = that.cpList;
					let isSave = false;
					for (var i = 0; i < arr.length; i++) {
						if(arr[i].cpId == obj.cpId) {
							isSave = true;
						}
					}
					if(!isSave) {
						arr = arr.concat(obj);
						that.cpList = arr;
						setTimeout(() => {
							uni.pageScrollTo({
								duration: 100,
								scrollTop: 100000
							})
						}, 88)
					} else {
						setTimeout(()=> {
							uni.showToast({
								title: '该产品已存在！',
								icon: 'none',
								duration: 2000
							})
						}, 88)
					}
					that.isTjcp = false;
				}
			},
			// 删除产品
			deleteCpFun: function(iii) {
				let item = that.cpList[iii];
				if(item._id) {
					uni.showModal({
						title: '提示',
						content: '是否确认要删除该产品？',
						success(res) {
							if(res.confirm) {
								uni.showLoading({
									title: '加载中...'
								})
								let reqData = {
									action: 'deleteCp',
									params: {
										_id: item._id
									}
								}
								crmBaoJiaDanApi(reqData)
								.then(res => {
									that.cpList.splice(iii, 1)
								})
							}
						}
					})
				} else {
					that.cpList.splice(iii, 1)
				}
			},
			// 售价修改
			cpPriceUpdateFun: function(iii, item) {
				let arr = that.cpList;
				let sellingPrice = item.sellingPrice;
				item.discount = ((item.sellingPrice / item.price) * 100).toFixed(2);
				that.$set(that.cpList, iii, item);
				that.cpCalcFun();
			},
			// 折扣修改
			cpDiscountUpdateFun: function (iii, item) {
				let arr = that.cpList;
				let discount = item.discount;
				item.sellingPrice = (item.price * discount / 100).toFixed(2);
				that.$set(that.cpList, iii, item);
				that.cpCalcFun();
			},
			// 产品总折扣修改
			cpzzkXgFun: function () {
				that.cpHzObj.sumPrice = (that.cpHzObj.discount * that.cpHzObj.price / 100).toFixed(2);
				that.bjPrice = that.cpHzObj.sumPrice;
			},
			// 产品最终价格修改
			cpzzPriceXgFun: function() {
				that.cpHzObj.discount = (that.cpHzObj.sumPrice / that.cpHzObj.price * 100).toFixed(2);
				that.bjPrice = that.cpHzObj.sumPrice;
			},
			// 订单价格修改
			orderPriceXgFun: function() {
				if(that.cpList.length > 0) {
					that.cpHzObj.sumPrice = that.bjPrice;
					that.cpHzObj.discount = (that.bjPrice / that.cpHzObj.price * 100).toFixed(2);
				}
			},
			// 计算金额
			cpCalcFun: function() {
				let arr = that.cpList;
				let bjPrice = 0;
				let zprice = 0;
				let totalNum = 0;
				for(var i = 0; i < arr.length; i++) {
					arr[i].totalPrice = (parseFloat(arr[i].sellingPrice) * arr[i].num).toFixed(2);
					bjPrice += parseFloat(arr[i].totalPrice);
					zprice += parseFloat(arr[i].price) * arr[i].num;
					totalNum += arr[i].num;
				}
				that.cpHzObj.price = zprice.toFixed(2);
				that.cpHzObj.discount = (bjPrice / that.cpHzObj.price * 100).toFixed(2);
				if(that.cpHzObj.discount == 'NaN') {
					that.cpHzObj.discount = 0;
				}
				that.cpHzObj.sumPrice = bjPrice.toFixed(2);
				that.cpHzObj.totalNum = totalNum;
				that.cpList = arr;
				if(bjPrice == 0) {
					that.bjPrice = '';
				} else {
					that.bjPrice = bjPrice.toFixed(2);
				}
			},
			// 提交按钮方法
			submitBtnFun: function() {
				if(!that.bjName) {
					uni.$myModalFun('报价名称不能为空，请输入！');
					return
				}
				if(!that.clientName) {
					uni.$myModalFun('报价单所属客户不能为空，请选择！');
					return
				}
				if(!that.bjPrice) {
					uni.$myModalFun('报价金额不能为空，请输入或者添加产品自动计算！');
					return
				}
				let req = {
					bjName: that.bjName,
					sjName: that.sjName,
					sjId: that.sjId,
					cpNum: that.cpList.length,
					// clientName: that.clientName,
					clientId: that.clientId,
					clientLxr: that.clientLxr,
					clientLxrId: that.clientLxrId,
					bjPrice: that.bjPrice,
					describe: that.describe,
					bjdDate: that.bjdDate,
					bjdDateTime: that.bjdDateTime,
					// cpList: that.cpList,
					fuZeRenId: that.fuZeRenId,
					fuZeRen: that.fuZeRen,
				}
				let dateArr = getDayFun();
				let nowDate = getNowDate().nowDate;
				let fgArr = nowDate.split(" ");
				let dateStr = (fgArr[0]).replace(/-/g, "");
				// if(that.cpList.length > 0) {
				// 	req.cpHzObj = that.cpHzObj;
				// }
				let reqObj = {
					cpList: that.cpList,
					req
				}
				let action = 'addBaoJiaDan';
				if (that.pageType == 'update') {
					action = 'update'
					reqObj._id = that.xgInfoObj._id;
					reqObj.req.update_date = new Date().getTime();
					uni.showLoading({
						title: '修改中...'
					})
				} else {
					reqObj.req.create_date = new Date().getTime();
					reqObj.req.update_date = new Date().getTime();
					reqObj.req.cjRen = uni.$userInfo.xingMing;
					reqObj.req.cjRenId = uni.$userInfo._id;
					reqObj.startDate = dateArr[0];
					reqObj.endDate = dateArr[1];
					reqObj.dateStr = parseInt(dateStr);
					uni.showLoading({
						title: '提交中...'
					})
				}
				let reqData = {
					action: action,
					params: reqObj
				}
				crmBaoJiaDanApi(reqData)
				.then(res => {
					if (action == 'update') {
						that.addCzjlFun();
						uni.showToast({
							title: '报价单修改成功',
							icon: 'none',
							duration: 1000,
							mask: true
						})
						setTimeout(()=> {
							uni.navigateBack()
						}, 1000)
					} else {
						uni.showToast({
							title: '报价单新增成功',
							icon: 'none',
							duration: 1000,
							mask: true
						})
						reqObj.req._id = res.result.id;
						reqObj.req.clientName = that.clientName;
						uni.$emit('addBjdItemInListFun')
						setTimeout(()=> {
							uni.redirectTo({
								url: './detail?index=0&fromDetail=true&alloAllocatedMasterID=' + res.result.id
							})
						}, 1000)
					}
				})
			},
			addCzjlFun: function(action, _id) {
				let czjlObj = {
					create_date: new Date().getTime(),
					czRen: uni.$userInfo._id,
					pageType: 'quotation',
				}
				let xgInfoObj = that.xgInfoObj;
				czjlObj.dataId = xgInfoObj._id;
				czjlObj.type = '编辑';
				czjlObj.newUpdate = new Date().getTime();
				czjlObj.oldUpdate = xgInfoObj.update_date;
				let content = [];
				let str = '';
				if(that.bjName != xgInfoObj.bjName) {
					str = '修改 报价单名称 ，由 "' + xgInfoObj.bjName + '" 变更为 "' + that.bjName + '"'
					content.push(str)
				}
				if(that.sjName != xgInfoObj.sjName) {
					str = '修改 商机 ，由 "' + xgInfoObj.sjName + '" 变更为 "' + that.sjName + '"'
					content.push(str)
				}
				if(that.clientName != xgInfoObj.clientName) {
					str = '修改 客户 ，由 "' + xgInfoObj.clientName + '" 变更为 "' + that.clientName + '"'
					content.push(str)
				}
				if(that.bjName != xgInfoObj.bjName) {
					str = '修改 报价单名称 ，由 "' + xgInfoObj.bjName + '" 变更为 "' + that.bjName + '"'
					content.push(str)
				}
				if(that.clientLxrId != xgInfoObj.clientLxrId) {
					str = '修改 联系人 ，由 "' + xgInfoObj.clientLxr + '" 变更为 "' + that.clientLxr + '"'
					content.push(str)
				}
				if(that.bjPrice != xgInfoObj.bjPrice) {
					str = '修改 报价金额 ，由 "' + xgInfoObj.bjPrice + '" 变更为 "' + that.bjPrice + '"'
					content.push(str)
				}
				if(that.describe != xgInfoObj.describe) {
					str = '修改 备注 ，由 "' + xgInfoObj.describe + '" 变更为 "' + that.describe + '"'
					content.push(str)
				}
				if(that.bjdDate != xgInfoObj.bjdDate) {
					str = '修改 报价日期 ，由 "' + xgInfoObj.bjdDate + '" 变更为 "' + that.bjdDate + '"'
					content.push(str)
				}
				if(that.fuZeRen != xgInfoObj.fuZeRen) {
					str = '修改 负责人 ，由 "' + xgInfoObj.fuZeRen + '" 变更为 "' + that.fuZeRen + '"'
					content.push(str)
				}
				czjlObj.content = content;
				uni.$czjlApiAddFun(czjlObj);
			},
			// 数据恢复
			setDataFun: function() {
				let obj = uni.$infoObj
				that.xgInfoObj = uni.$infoObj
				that.bjName = obj.bjName
				that.sjName = obj.sjName
				that.sjId = obj.sjId
				that.clientName = obj.clientName
				that.clientId = obj.clientId
				that.clientLxr = obj.clientLxr
				that.clientLxrId = obj.clientLxrId
				that.bjPrice = obj.bjPrice
				that.describe = obj.describe
				that.bjdDate = obj.bjdDate
				that.bjdDateTime = obj.bjdDateTime
				// that.cpList = obj.cpList
				// that.cpHzObj = obj.cpHzObj || {}
				that.fuZeRenId = obj.fuZeRenId
				that.fuZeRen = obj.fuZeRen
				that.getCpByIdFun()
			},
			getCpByIdFun: function() {
				let reqData = {
					action: 'getCp',
					params: {
						bjdId: that.xgInfoObj._id
					}
				}
				uni.showLoading({
					title: '加载中...'
				})
				crmBaoJiaDanApi(reqData)
				.then(res => {
					let data = res.result.data;
					if(data.length > 0) {
						let cpHzObj = {
							discount: 0,
							price: 0,
							sumPrice: 0,
							totalNum: 0
						};
						for(var i = 0; i < data.length; i++) {
							var obj = data[i];
							obj.discount = (obj.sellingPrice / obj.price * 100).toFixed(2);
							obj.totalPrice = (obj.sellingPrice * obj.num).toFixed(2);
							data[i] = obj;
							cpHzObj.price += parseFloat(obj.price) * obj.num;
							cpHzObj.sumPrice += parseFloat(obj.sellingPrice) * obj.num;
							cpHzObj.totalNum += obj.num;
						}
						cpHzObj.discount = cpHzObj.sumPrice / cpHzObj.price * 100;
						that.cpHzObj = cpHzObj;
						that.cpList = data;
					}
				})
			},
			// 根据商机id查询产品
			getCpBysjIdFun: function(sjId) {
				let reqData = {
					action: 'getCp',
					params: {
						sjId: sjId
					}
				}
				uni.showLoading({
					title: '加载中...'
				})
				crmShangJiApi(reqData)
				.then(res => {
					let data = res.result.data;
					if(data.length > 0) {
						let cpHzObj = {
							discount: 0,
							price: 0,
							sumPrice: 0,
							totalNum: 0
						};
						for(var i = 0; i < data.length; i++) {
							var obj = data[i];
							obj._id = '';
							obj.discount = (obj.sellingPrice / obj.price * 100).toFixed(2);
							obj.totalPrice = (obj.sellingPrice * obj.num).toFixed(2);
							data[i] = obj;
							cpHzObj.price += parseFloat(obj.price) * obj.num;
							cpHzObj.sumPrice += parseFloat(obj.sellingPrice) * obj.num;
							cpHzObj.totalNum += obj.num;
						}
						cpHzObj.discount = cpHzObj.sumPrice / cpHzObj.price * 100;
						that.cpHzObj = cpHzObj;
						that.cpList = data;
					}
				})
			},
		}
	}
</script>

<style>
	page {
		background-color: #F8F8F8;
		padding-bottom: 260rpx;
	}
	.u-radio {
		width: 200rpx !important;
	}
	.submitView {
		width: 100%;
		padding: 16rpx 0 26rpx;
		background-color: #FFFFFF;
		position: fixed;
		bottom: 0;
		left: 0;
		border-top: 1rpx solid #f1f1f1;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 100;
	}
	.submitBtn {
		width: 666rpx;
	}
	.productBox {
		background-color: #FFFFFF;
		margin-top: 32rpx;
		padding: 26rpx 0;
	}
	.tjcpName {
		width: 686rpx;
		height: 40rpx;
		font-size: 16px;
		font-weight: bold;
		border-left: 6rpx solid #007AFF;
		padding-left: 12rpx;
		margin-left: 26rpx;
		margin-top: 26rpx;
	}
	.cpInput {
		width: 150rpx !important;
		margin-right: 12rpx;
	}
	.cpInput>input {
		box-sizing: border-box;
		border: 1rpx solid #DDDDDD;
		width: 100%;
		height: 60rpx;
		border-radius: 10rpx;
		padding: 0 10rpx;
	}
	.cpInput1 {
		width: 200rpx !important;
		margin-right: 12rpx;
	}
	.cpInput1>input {
		box-sizing: border-box;
		border: 1rpx solid #DDDDDD;
		width: 100%;
		height: 60rpx;
		border-radius: 10rpx;
		padding: 0 10rpx;
	}
	.clearIcon {
		position: absolute;
		right: 6rpx;
		top: 6rpx;
	}
	.greenPrice {
		font-size: 16px;
		color: #19BE6B !important;
		font-weight: bold;
	}
	.disFlex {
		display: flex;
		align-items: center;
		margin-bottom: 8rpx;
	}
	.inputName {
		color: #ADADAD;
		font-size: 16px;
	}
	.addHKQS {
		display: flex;
		align-items: center;
		padding: 16rpx 26rpx;
		font-size: 15px;
		font-weight: bold;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>
