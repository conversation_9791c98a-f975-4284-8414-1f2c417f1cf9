<template>
	<view class="myCard" >
		<view class="cardTopName">盘点单号：{{item.order_no}}</view>
		<view class="cardRow">
			<view>盘点日期：</view>
			<view>{{this.$u.timeFormat(item.check_time,'yyyy-mm-dd')}}</view>
		</view>
		<view class="cardRow">
			<view>仓库名称：</view>
			<view>{{item.warehouse_name}}</view>
		</view>
		<view class="cardRow">
			<view>仓位名称：</view>
			<view>{{item.warehouse_bin_name}}</view>
		</view>
		<view class="cardRow">
			<view>创 建 人：</view>
			<view>{{item.create_user_name}} {{this.$u.timeFormat(item.create_time,'yyyy-mm-dd hh:ss:MM')}}</view>
		</view>
		<view class="cardRow">
			<view>审 核 人：</view>
			<view>{{item.auditor_name}} {{this.$u.timeFormat(item.audit_date,'yyyy-mm-dd hh:ss:MM')}}</view>
		</view>
		<view v-if="!isDetail && !isSelect" class="lookDetail">
			<text>查看详情</text>
			<u-icon name="arrow-right" size="36"></u-icon>
		</view>
	</view>
</template>

<script>	
	export default {
		props: {
			item: {
				type: Object,
				default: () => {}
			},
			index: {
				type: Number,
				default: 0
			},
			isSelect: {
				type: Boolean,
				default: false
			},
			isDetail: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				
			}
		},
		methods: {
			onlaunch(e) {
				//console.log("---->>>" + item.OpDate);
			},
				
		}
	}
</script>

<style>
	
</style>

