<template>
	<view>
		<u-form ref="uForm">
			<view class="flex-white-plr26 ptb10 mt32 bdb_f5">
				<text class="mr26">进仓凭证</text>
				<u-input disabled placeholder="自动生成" v-model="GoodsInBillNo" />
				<text class="mr26">进仓日期</text> {{$u.timeFormat(GoodsInBillDate, 'yyyy-mm-dd')}}
			</view>
			<view @click="pickerSelectFun('单据类型')" class="flex-white-plr26 ptb20 bdb_f5">
				<text class="mr26">单据类型<text class="redXingh">*</text></text>
				<view :class="BillTypeName ? '' : 'cBlack'"> {{BillTypeName ? BillTypeName : '请选择'}}
					<u-icon class="ml26" name="arrow-right" size="40" color="#888888"></u-icon>
				</view>
			</view>
			<view @click="pickerSelectFun('仓库名称')" class="flex-white-plr26 ptb20 bdb_f5">
				<text class="mr26">仓库名称<text class="redXingh">*</text></text>
				<view :class="StoreName ? '' : 'cBlack'"> {{StoreName ? StoreName : '请选择'}}
					<u-icon class="ml26" name="arrow-right" size="40" color="#888888"></u-icon>
				</view>
			</view>
			<view>
				<text class="flex-white-plr26 ptb20 bdb_f5">来源仓库：{{FromStoreName}}</text>
			</view>
			<view class="flex-white-plr26 ptb20 bdb_f5" @click="selectCustomer">
				<text class="mr26">往来单位<text class="redXingh">*</text></text>
				<view :class="CustomerName ? '' : 'cBlack'"> {{CustomerName ? CustomerName : '请选择'}}
					<u-icon class="ml26" name="arrow-right" size="40" color="#888888"></u-icon>
				</view>
			</view>
			<view @click="pickerSelectFun('营销部门')" class="flex-white-plr26 ptb20 bdb_f5">
				<text class="mr26">营销部门<text class="redXingh">*</text></text>
				<view :class="PlanDepartmentName ? '' : 'cBlack'"> {{PlanDepartmentName ? PlanDepartmentName : '请选择'}}
					<u-icon class="ml26" name="arrow-right" size="40" color="#888888"></u-icon>
				</view>
			</view>
			<view>
				<text class="flex-white-plr26 ptb20 bdb_f5">销 售 员：{{SaleUserName}}</text>
			</view>
			<!--
			<view @click="pickerSelectFun('销售员')" class="flex-white-plr26 ptb20 bdb_f5">
				<text class="mr26">销 售 员<text class="redXingh">*</text></text>
				<view :class="SaleUserName ? '' : 'cBlack'"> {{SaleUserName ? SaleUserName : '请选择'}}
					<u-icon class="ml26" name="arrow-right" size="40" color="#888888"></u-icon>
				</view>
			</view>
			-->
			<view class="flex-white-plr26-column ptb20 mt32">
				<view style="margin-bottom: 8rpx;">
					<text>备注内容</text>
				</view>
				<u-input v-model="Remark" maxlength="-1" type="textarea" :border="true" :height="100"
					:auto-height="true" />
			</view>
			<u-form-item label-width="150" label="条码资料:">
				<input type="text" maxlength="-1" v-model="QRBarCode" :focus="testFocus1" style="width:150px;"
					@confirm="GoodsInBillDetailScan" />
				<checkbox-group @change="AllCrockNoCheckChange">
					<checkbox ref="checkBoxRef" :checked="AllCrockNoScanStatus">整缸</checkbox>
				</checkbox-group>
				<checkbox-group @change="BarCodeDelChange">
					<checkbox ref="checkBoxRef" :checked="BarCodeDelStatus">删除</checkbox>
				</checkbox-group>
			</u-form-item>
			<u-form-item>
				<text class="title">{{BillDataMessage}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">成品编号：{{FabricGoodsNo}}</text>
				<text class="title" style="width:200px;">成品重量：{{GoodsQty}}KG</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">成品色号：{{GoodsCodeNo}}</text>
				<text class="title" style="width:200px;">成品颜色：{{GoodsCodeName}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">成品缸号：{{CrockNo}}</text>
				<text class="title" style="width:200px;">缸号卷号：{{GoodsBillNo}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">本单条数：{{BillSumRoll}}条</text>
				<text class="title" style="width:200px;">本单重量：{{BillSumQty}}KG</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">本色条数：{{GoodsCodeSumRoll}}条</text>
				<text class="title" style="width:200px;">本色重量：{{GoodsCodeSumQty}}KG</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">本缸条数：{{CrockNoSumRoll}}条</text>
				<text class="title" style="width:200px;">本缸重量：{{CrockNoSumQty}}KG</text>
			</u-form-item>
		</u-form>
		<view class="u-demo-area">
			<u-toast ref="uToast"></u-toast>
			<wyb-table ref="table" :headers="headersMaster" :contents="GoodsDetailList"
				@onCellClick="GetDetailCrockNoListData" />
			<!--	<wyb-table ref="table" :headers="headersDetail" :contents="GoodsCrockNoDataList"  height="600rpx" /> !-->
		</view>

		<!--提交按钮-->
		<view class="submitView">
			<u-button type="primary" style="width:100px;" class="submitBtn"
				:ripple="true" ripple-bg-color="#909399" @click="submitBtnFun">
				{{pageType ? '保存' : '提交'}}
			</u-button>

			<u-button type="primary" style="width:100px;" class="primary"
				:disabled="false" :ripple="true" ripple-bg-color="#909399"
				@click="commitBtnFun"> {{CommitType ? '消审' : '审核'}}
			</u-button>
		</view>
		<!--组件-->
		<u-select v-model="selectShow" :list="selectList" @confirm="selectConfirmFun"></u-select>
	</view>
</template>

<script>
	let that = '';
	import util, {
		parFabricGoodsBarCode2D,
		parYarnGoodsBarCode2D,
		playSuccessAudio,
		playErrorAudio
	} from '../../common/util';
	import wybTable from '@/components/wyb-table/wyb-table.vue';
	export default {
		data() {
			return {
				disabled: false,
				selectShow: false,
				selectList: [],
				selectType: '',
				pageType: '',
				CommitType: '',
				CommitProcName: '',
				actionSheetShow: false,
				testFocus0: true,
				testFocus1: false,
				QRBarCode: '',
				AllCrockNoScanStatus: false,
				BarCodeDelStatus: false,
				GoodsInBillID: 0,
				GoodsInBillNo: '',
				GoodsInBillDate: '',
				GoodsInBillMasterNo: '',
				BillBusinessID: '',
				BillTypeName: '',
				StoreNameID: '',
				StoreName: '',
				FromStoreNameID: '',
				FromStoreName: '',
				CustomerID: '',
				CustomerName: '',
				PlanDepartmentID: '',
				PlanDepartmentName: '',
				SaleUserID: '',
				SaleUserName: '',
				Remark: '',
				FabricGoodsNo: '',
				FabricGoodsName: '',
				GoodsCodeNo: '',
				GoodsCodeName: '',
				CrockNo: '',
				GoodsBillNo: '',
				GoodsQty: 0,
				testFocus0: false,
				testFocus1: true,
				BillSumRoll: 0,
				BillSumQty: 0,
				GoodsCodeSumRoll: 0,
				GoodsCodeSumQty: 0,
				CrockNoSumRoll: 0,
				CrockNoSumQty: 0,
				BarCodeDelStatus: false,
				GoodsDetailList: [],
				BillBusinessDataList: [],
				StoreNameDataList: [],
				FromStoreNameDataList: [],
				PlanDepartmentDataList: [],
				SaleUserDataList: [],
				BillDataMessage: '',
				headersMaster: [{
					label: '成品编号',
					key: 'FabricGoodsNo'
				}, {
					label: '成品色号',
					key: 'GoodsCodeNo'
				}, {
					label: '成品颜色',
					key: 'GoodsCodeName'
				}, {
					label: '成品缸号',
					key: 'CrockNo'
				}, {
					label: '条数',
					key: 'Roll'
				}, {
					label: '重量',
					key: 'Qty'
				}],
			}
		},
		watch: {
			cpList: function(val, old) {
				that.cpCalcFun();
			}
		},
		onLoad(e) {
			that = this;

			//this.getSaleUserData();

			this.getBillBusinessData();

			setTimeout(() => {
				this.getStoreNameData();
			}, 500);

			setTimeout(() => {
				this.getPlanDepartmentData();
			}, 1000);

			if (getApp().globalData.StoreTypeNo.toUpperCase() == 'STOREFABRICGOODS') {
				this.StoreNameID = getApp().globalData.StoreNameID;
				this.StoreName = getApp().globalData.StoreName;
			};

			if (getApp().globalData.PlanDepartmentID != '') {
				this.PlanDepartmentID = getApp().globalData.PlanDepartmentID;
				this.PlanDepartmentName = getApp().globalData.PlanDepartmentName;
			}

			uni.$on('bjdKehuBindFun', that.bjdKehuBindFun)
			uni.$on('chanpinBindFun', that.chanpinBindFun)
			uni.$on('bjdLxrBindFun', that.bjdLxrBindFun)
			uni.$on('shangjiBindFun', that.shangjiBindFun)
		},

		onBackPress() {
			uni.$off('bjdKehuBindFun', that.bjdKehuBindFun)
			uni.$off('chanpinBindFun', that.chanpinBindFun)
			uni.$off('bjdLxrBindFun', that.bjdLxrBindFun)
			uni.$off('shangjiBindFun', that.shangjiBindFun)
		},
		methods: {

			playSuccess() {
				util.playSuccessAudio();
			},
			playError() {
				util.playErrorAudio();
			},

			AllCrockNoCheckChange: function() {
				this.AllCrockNoScanStatus = !this.AllCrockNoScanStatus;
			},

			BarCodeDelChange: function() {
				this.BarCodeDelStatus = !this.BarCodeDelStatus;
			},
			// 展示相应数据选择框
			pickerSelectFun: function(str) {

				that.selectList = [];
				if (str == '仓库名称') {
					that.selectList = this.StoreNameDataList;
				} else if (str == '来源仓库') {
					that.selectList = this.FromStoreNameDataList;
				} else if (str == '单据类型') {
					console.log("---单据类型->>>" + str);
					that.selectList = this.BillBusinessDataList;
				} else if (str == '营销部门') {
					that.selectList = this.PlanDepartmentDataList;
				} else if (str == '销售员') {
					that.selectList = this.SaleUserDataList;
				}
				console.log(JSON.stringify(that.selectList));
				that.selectShow = true;
				that.selectType = str;
			},
			// 选择框选中事件
			selectConfirmFun: function(e) {
				if (that.selectType == '仓库名称') {
					that.StoreNameID = e[0].value;
					that.StoreName = e[0].label;
				} else if (that.selectType == '来源仓库') {
					that.FromStoreNameID = e[0].value;
					that.FromStoreName = e[0].label;
				} else if (that.selectType == '单据类型') {
					that.BillBusinessID = e[0].value;
					that.BillTypeName = e[0].label;
				} else if (str == '营销部门') {
					that.PlanDepartmentID = e[0].value;
					that.PlanDepartmentName = e[0].label;
				} else if (str == '销售员') {
					that.SaleUserID = e[0].value;
					that.SaleUserName = e[0].label;
				}
			},

			getStoreNameData() {
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetStoreNameList',
							params: [{
								name: 'TypeNo',
								value: '%StoreFabricGoods%'
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							console.log("--->>仓库名称<<<--" + JSON.stringify(res.data));
							var aResultData = res.data.data;
							for (var i = 0; i < aResultData.length; i++) {
								this.StoreNameDataList.push({
									value: aResultData[i].StoreNameID,
									label: aResultData[i].StoreName
								});
								this.FromStoreNameDataList.push({
									value: aResultData[i].StoreNameID,
									label: aResultData[i].StoreName
								})
							}
						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.StoreNameDataList = [];
							this.FromStoreNameDataList = [];
						} else {
							this.StoreNameDataList = [];
							this.FromStoreNameDataList = [];
						}
					},
					fail: (error) => {
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},

			getPlanDepartmentData() {
				console.log("--->>>营销部门<<<---");
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetPlanDepartmentOwnerList',
							params: [{
								name: 'LoginID',
								value: getApp().globalData.LoginID
							}, {
								name: 'DefaultLoginID',
								value: getApp().globalData.LoginID
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							console.log("---->" + JSON.stringify(res.data));
							var aResultData = res.data.data;
							for (var i = 0; i < aResultData.length; i++) {
								this.PlanDepartmentDataList.push({
									value: aResultData[i].PlanDepartmentID,
									label: aResultData[i].PlanDepartmentName
								})
							}
						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.PlanDepartmentDataList = [];
						} else {
							this.PlanDepartmentDataList = [];
						}
					},
					fail: (error) => {
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},

			getSaleUserData() {
				console.log("--->>>销售员<<<---");
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.BaseData.GetSaleUserData',
							params: []
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							console.log("---->" + JSON.stringify(res.data));
							var aResultData = res.data.data;
							for (var i = 0; i < aResultData.length; i++) {
								this.SaleUserDataList.push({
									value: aResultData[i].SaleUserID,
									label: aResultData[i].SaleUserName
								})
							}
						} else {
							this.SaleUserDataList = [];
						}
					},
					fail: (error) => {
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},

			GetStoreStationName: function() {
				console.log('>>>>>' + this.StoreStationNo + '<<<<');
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetStoreStationName',
							params: [{
								name: 'No',
								value: this.StoreStationNo
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							this.playSuccess();
							var aResultData = res.data.data;
							for (var i = 0; i < aResultData.length; i++) {
								this.StoreStationName = aResultData[i].StoreStationName;
							};
							this.testFocus0 = false;
							this.$nextTick(() => {
								this.testFocus1 = true;
								this.testFocus0 = false;
							});
						} else {
							this.playError();
							this.StoreStationName = "",
								this.testFocus0 = false;
							this.$nextTick(() => {
								this.testFocus1 = false;
								this.testFocus0 = true;
							});
							this.StoreNameID = 0,
								this.BillDataMessage = '不存在当前的仓位资料，请确认！';
						}
					},
					fail: (error) => {
						this.playError();
						this.BillDataMessage = '连接服务器出错，请检查后台服务是否启动！' + res.data.msg;
					},
				})
			},

			getBillBusinessData() {
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetBillBusinessList',
							params: [{
								name: 'TypeStatus',
								value: '1'
							}, {
								name: 'TypeNo',
								value: 'BusinessTypeStoreGoods'
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count == 1) {
							var aResultData = res.data.data;
							for (var i = 0; i < aResultData.length; i++) {
								this.BillBusinessID = aResultData[i].BillBusinessID;
								this.BillTypeName = aResultData[i].BillTypeName;
								this.BillBusinessDataList.push({
									value: aResultData[i].BillBusinessID,
									label: aResultData[i].BillTypeName
								})
							}
						} else if (res.data.status == 0 && res.data.count > 0) {
							console.log("--->>单据类型<<--" + JSON.stringify(res.data));
							var aResultData = res.data.data;
							for (var i = 0; i < aResultData.length; i++) {
								this.BillBusinessDataList.push({
									value: aResultData[i].BillBusinessID,
									label: aResultData[i].BillTypeName
								})
							}
						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.BillBusinessDataList = [];
						} else {
							this.BillBusinessDataList = [];
						}
					},
					fail: (error) => {
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},

			GoodsInBillDetailData: function() {
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetStoreGoodsBusinessInDetailDataSQL',
							params: [{
								name: 'BillID',
								value: this.GoodsInBillID
							}]
						},
					},
					success: (res) => {
						this.triggered = false;
						let data = res.data.data;
						console.log("---->" + JSON.stringify(res.data));
						if (res.data.status == 0 && res.data.count > 0) {
							var aResultData = res.data.data;
							this.GoodsDetailList = res.data.data;
							this.CommitType = aResultData[0].CommitType;
							this.CommitProcName = aResultData[0].CommitProcName;
							this.BillSumRoll = 0;
							this.BillSumQty = 0;
							for (var i = 0; i < aResultData.length; i++) {
								if (parseFloat(aResultData[i].Roll) > 0) {
									this.BillSumRoll = this.BillSumRoll + aResultData[i].Roll;
								}

								if (parseFloat(aResultData[i].Qty) > 0) {
									this.BillSumQty = this.BillSumQty + aResultData[i].Qty;
								}
							};
							this.BillSumRoll = this.BillSumRoll.toFixed(2);
							this.BillSumQty = this.BillSumQty.toFixed(2);

						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.GoodsDetailList = [];
						} else {
							this.GoodsDetailList = [];
						}

					},
				})
			},

			// 日期修改
			bindDateChange: function(e) {
				that.bjdDate = e.detail.value;
				that.bjdDateTime = new Date(e.detail.value + ' 00:00:00').getTime()
			},
			// 展示相应数据选择框

			scanFun: function() {
				uni.scanCode({
					success(res) {
						that.code = res.result;
					}
				})
			},

			// 选择所属客户
			selectCustomer: function() {
				let aTypeName = "客户";
				if (this.BillTypeName == '成品销售进仓单') {
					aTypeName = "客户"
				}else if (this.BillTypeName == '成品退染进仓单') {
					aTypeName = "染整厂"
				}else if (this.BillTypeName == '销售调拨进仓单') {
					aTypeName = "客户"
				}else if (this.BillTypeName == '内部调拨进仓单') {
					aTypeName = "客户"
				}else if (this.BillTypeName == '成品采购进仓单') {
					aTypeName = "成品供应商"
				}else if (this.BillTypeName == '成品其它进仓单') {
					aTypeName = ""
				};

				uni.navigateTo({
					url: '../basedata/customer/customer?type=' + aTypeName + ''
				})

			},

			// 选择所属客户
			selectBillBusiness: function() {
				let aTypeName = "客户";
				if (this.BillTypeName == '成品销售进仓单') {
					aTypeName = "客户"
				}else if (this.BillTypeName == '成品退染进仓单') {
					aTypeName = "染整厂"
				}else if (this.BillTypeName == '销售调拨进仓单') {
					aTypeName = "客户"
				}else if (this.BillTypeName == '内部调拨进仓单') {
					aTypeName = "客户"
				}else if (this.BillTypeName == '成品采购进仓单') {
					aTypeName = "成品供应商"
				}else if (this.BillTypeName == '成品其它进仓单') {
					aTypeName = ""
				};

				uni.navigateTo({
					url: '../basedata/BillBusiness/BillBusinessList?TypeStatus=1&TypeNo=BusinessTypeStoreGoods'
				})

			},

			// 绑定客户
			bjdKehuBindFun: function(e) {
				console.log("CustomerName===" + e.CustomerName);
				that.CustomerID = e.CustomerID;
				that.CustomerName = e.CustomerName;
				that.PlanDepartmentID = e.PlanDepartmentID;
				that.PlanDepartmentName = e.PlanDepartmentName;
				that.SaleUserID = e.SaleUserID;
				that.SaleUserName = e.SaleUserName;
				that.SaleCustomerAddress = e.CustomerAddress + ' ' + e.CustomerPhone + ' ' + e.CustomerLinkName;
			},

			// 提交按钮方法
			submitBtnFun: function() {
				if (this.PlanDepartmentName == '') {
					this.playError();
					this.BillDataMessage = '营销部门不能为空，请先输入营销部门！';
					return;
				}
				if (this.StoreName == '') {
					this.playError();
					this.BillDataMessage = '仓库名称不能为空，请先输入仓库名称！';
					return;
				}
				if (this.BillTypeName == '') {
					this.playError();
					this.BillDataMessage = '单据类型不能为空，请先输入单据类型！';
					return;
				}
				if (this.CustomerName == '') {
					this.playError();
					this.BillDataMessage = '往来单位不能为空，请先输入往来单位！';
					return;
				}

				if (this.GoodsInBillID > 0) {
					this.playError();
					this.BillDataMessage = '当前单据已经提交，不能重复提交！';
					return;
				}

				uni.request({
					url: util.apiurl + 'rest/db/storedproc',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							proc_name: 'Usp_APP_StoreGoodsBusinessInBillMaster',
							method: 'open_proc',
							params: [{
									name: '@BillMasterID',
									value: this.GoodsInBillID
								},{
									name: '@BillBusinessID',
									value: this.BillBusinessID
								},{
									name: '@StoreNameID',
									value: this.StoreNameID
								},{
									name: '@FromStoreNameID',
									value: this.FromStoreNameID
								},{
									name: '@PlanDepartmentID',
									value: this.PlanDepartmentID
								},{
									name: '@CustomerID',
									value: this.CustomerID
								},{
									name: '@SaleUserID',
									value: this.SaleUserID
								},{
									name: '@Remark',
									value: this.Remark
								},{
									name: '@UserName',
									value: getApp().globalData.UserName
								}
							]
						},
					},
					success: (res) => {
						if (res.data.status == 0) {
							var aResultData = JSON.parse(res.data.data);
							if (aResultData.BillDataStatus == '0' && aResultData.BillDataMessage ==
								'SUCCESS') {
								this.playSuccess();
								this.GoodsInBillID = aResultData.GoodsInBillID;
								this.GoodsInBillNo = aResultData.GoodsInBillNo;
								this.GoodsInBillDetailData();
								this.BillDataMessage = "";
								this.testFocus1 = false;
								this.$nextTick(() => {
									this.testFocus1 = true;
								});
							} else {
								this.playError();
								this.BillDataMessage = '提交出错！' + aResultData.BillDataMessage;
								return;
							}
						} else {
							this.playError();
							this.BillDataMessage = '提交出错，' + res.data.msg
							return;
						}
					},
					fail: (error) => {
						this.playError();
						this.BillDataMessage = '连接服务器出错，请检查后台服务是否启动！' + res.data.msg;
					},
				})
			},

			//审核按钮方法
			commitBtnFun: function() {
				if (this.GoodsInBillID <= 0) {
					this.playError();
					this.BillDataMessage = '当前单据未提交，不能审核！';
					return;
				}

				if (this.GoodsInBillID <= 0) {
					this.playError();
					this.BillDataMessage = '当前单据未提交，不能审核！';
					return;
				}

				var aCommitRecallName = '审核';
				if (this.CommitType == ''){
					aCommitRecallName = '审核';
				} else {
					aCommitRecallName = '消审';
				}

				if (this.CommitProcName == ''){
					this.playError();
					this.BillDataMessage = '当前'+ aCommitRecallName +'操作配置有误，不能审核！';
					return;
				}

				{
					uni.request({
						url: util.apiurl + 'rest/db/storedproc',
						data: {
							token: getApp().globalData.Token,
							format: 'json',
							data: {
								db_name: getApp().globalData.AppDBName,
								proc_name: this.CommitProcName,
								method: 'open_proc',
								params: [{
										name: '@BillID',
										value: this.GoodsInBillID
									},{
										name: '@BillNo',
										value: this.GoodsInBillNo
									},{
										name: '@UserName',
										value: getApp().globalData.UserName
									}
								]
							},
						},
						success: (res) => {
							if (res.data.status == 0) {
								var aResultData = JSON.parse(res.data.data);
								if (aResultData.BillDataStatus == '0' && aResultData.BillDataMessage == 'SUCCESS') {
									this.playSuccess();
									this.BillDataMessage = aCommitRecallName + "成功！";

									if (aCommitRecallName == '审核'){
										this.CommitType = '已审核'
									} else {
										this.CommitType = ''
									}
								} else {
									this.playError();
									this.BillDataMessage = aCommitRecallName + '出错！' + aResultData.BillDataMessage;
									return;
								}
							} else {
								this.playError();
								this.BillDataMessage = aCommitRecallName + '出错，' + res.data.msg
								return;
							}
						},
						fail: (error) => {
							this.playError();
							this.BillDataMessage = '连接服务器出错，请检查后台服务是否启动！' + res.data.msg;
						},
					})
				}
			},

			GoodsInBillDetailScan() {
				if (this.StoreNameID == 0 && this.GoodsInBillNo == '') {
					this.playError();
					this.BillDataMessage = '请先新增单据';
					return;
				}
				console.log("this.QRBarCode ---->>" + this.QRBarCode);

				let aBarCodeDelStatus = 0;
				if (this.BarCodeDelStatus){
					aBarCodeDelStatus = '1';
				};

				let aAllCrockNoScanStatus = 0;
				if (this.AllCrockNoScanStatus){
					aAllCrockNoScanStatus = '1';
				};

				uni.request({
					url: util.apiurl + 'rest/db/storedproc',
					data: {
						token: getApp().globalData.Token,
						data: {
							db_name: getApp().globalData.AppDBName,
							proc_name: 'Usp_APP_StoreGoodsBusinessInBillDetail',
							method: 'open_proc',
							params: [{
									name: '@BillMasterID',
									value: this.GoodsInBillID
								},
								{
									name: '@QRBarCode',
									value: this.QRBarCode
								},
								{
									name: '@BarCodeDelStatus',
									value: aBarCodeDelStatus
								},
								{
									name: '@AllCrockNoScanStatus',
									value: aAllCrockNoScanStatus
								},
								{
									name: '@StoreStationName',
									value: this.StoreStationName
								},
								{
									name: '@UserName',
									value: getApp().globalData.UserName
								}
							]
						},
					},
					success: (res) => {
						if (res.data.status == 0) {
							console.log(res.data.data.toString());
							var aResultData = JSON.parse(res.data.data);
							if (aResultData.BillDataStatus == '0' && aResultData.BillDataMessage == 'SUCCESS') {
								this.playSuccess();
								this.FabricGoodsNo = aResultData.FabricGoodsNo;
								this.FabricGoodsName = aResultData.FabricGoodsName;
								this.GoodsCodeNo = aResultData.GoodsCodeNo;
								this.GoodsCodeName = aResultData.GoodsCodeName;
								this.CrockNo = aResultData.CrockNo;
								this.GoodsBillNo = aResultData.GoodsBillNo;
								this.GoodsQty = aResultData.GoodsQty;
								this.BillSumRoll = parseFloat(aResultData.BillSumRoll);
								this.BillSumQty = parseFloat(aResultData.BillSumQty);
								this.GoodsCodeSumRoll = parseFloat(aResultData.GoodsCodeSumRoll);
								this.GoodsCodeSumQty = parseFloat(aResultData.GoodsCodeSumQty);
								this.CrockNoSumRoll = parseFloat(aResultData.CrockNoSumRoll);
								this.CrockNoSumQty = parseFloat(aResultData.CrockNoSumQty);
								this.QRBarCode = '';
								this.BillDataMessage = '扫描成功！';
								this.GoodsInBillDetailData();
								this.testFocus1 = false;
								this.$nextTick(() => {
									this.testFocus1 = true;
								});
							} else {
								this.playError();
								this.BillDataMessage = '进仓扫描出错，' + aResultData.BillDataMessage;
								this.QRBarCode = '';
								this.testFocus1 = false;
								this.$nextTick(() => {
									this.testFocus1 = true;
								});
								return;
							}

						} else {
							this.playError();
							this.QRBarCode = '';
							this.BillDataMessage = '进仓扫描出错，' + res.data.msg;
							this.testFocus1 = false;
							this.$nextTick(() => {
								this.testFocus1 = true;
							});
							return;
						}
					},
					fail: (error) => {
						this.playError();
						this.QRBarCode = '';
						this.testFocus1 = false;
						this.$nextTick(() => {
							this.testFocus1 = true;
							this.testFocus0 = false;
						});
						this.BillDataMessage = '进仓扫描出错，' + res.data.msg;
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},
		}
	}
</script>

<style>
	page {
		background-color: #F8F8F8;
		padding-bottom: 260rpx;
	}

	.u-radio {
		width: 200rpx !important;
	}

	.submitView {
		width: 100%;
		padding: 16rpx 0 26rpx;
		background-color: #FFFFFF;
		position: fixed;
		bottom: 0;
		left: 0;
		border-top: 1rpx solid #f1f1f1;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 100;
	}

	.submitBtn {
		width: 666rpx;
	}

	.productBox {
		background-color: #FFFFFF;
		margin-top: 32rpx;
		padding: 26rpx 0;
	}

	.tjcpName {
		width: 686rpx;
		height: 40rpx;
		font-size: 16px;
		font-weight: bold;
		border-left: 6rpx solid #007AFF;
		padding-left: 12rpx;
		margin-left: 26rpx;
		margin-top: 26rpx;
	}

	.cpInput {
		width: 150rpx !important;
		margin-right: 12rpx;
	}

	.cpInput>input {
		box-sizing: border-box;
		border: 1rpx solid #DDDDDD;
		width: 100%;
		height: 60rpx;
		border-radius: 10rpx;
		padding: 0 10rpx;
	}

	.cpInput1 {
		width: 200rpx !important;
		margin-right: 12rpx;
	}

	.cpInput1>input {
		box-sizing: border-box;
		border: 1rpx solid #DDDDDD;
		width: 100%;
		height: 60rpx;
		border-radius: 10rpx;
		padding: 0 10rpx;
	}

	.clearIcon {
		position: absolute;
		right: 6rpx;
		top: 6rpx;
	}

	.greenPrice {
		font-size: 16px;
		color: #19BE6B !important;
		font-weight: bold;
	}

	.disFlex {
		display: flex;
		align-items: center;
		margin-bottom: 8rpx;
	}

	.inputName {
		color: #ADADAD;
		font-size: 16px;
	}

	.addHKQS {
		display: flex;
		align-items: center;
		padding: 16rpx 26rpx;
		font-size: 15px;
		font-weight: bold;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>
