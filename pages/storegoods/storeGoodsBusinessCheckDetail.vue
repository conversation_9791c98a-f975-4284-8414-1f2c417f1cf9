<template>
	<view class="wrap">
		<u-form ref="uForm">
			<u-form-item>
				<text class="title" style="width:200px;">单号：{{GoodsCheckBillNo}}</text>
				<text class="title" style="width:200px;">日期：{{GoodsCheckBillDate}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">仓库名称：{{StoreName}}</text>
				<text class="title" style="width:200px;">仓位名称：{{StoreStationName}}</text>
			</u-form-item>
			<u-form-item label-width="150" label="条码资料:">
				<input type="text" v-model="QRBarCode" maxlength="-1" style="width:200px;"
					@confirm="GoodsCheckBillDetailScan" />
				<checkbox-group @change="BarCodeDelChange">
					<checkbox ref="checkBoxRef" :checked="BarCodeDelStatus">删除</checkbox>
				</checkbox-group>
			</u-form-item>
			<u-form-item>
				<text class="title">{{BillDataMessage}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">成品编号：{{FabricGoodsNo}}</text>
				<text class="title" style="width:200px;">成品重量：{{GoodsQty}}KG</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">成品色号：{{GoodsCodeNo}}</text>
				<text class="title" style="width:200px;">成品颜色：{{GoodsCodeName}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">成品缸号：{{CrockNo}}</text>
				<text class="title" style="width:200px;">缸号卷号：{{GoodsBillNo}}</text>

			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">本架盘前：{{BillSumOldRoll}}条</text>
				<text class="title" style="width:200px;">本架实盘：{{BillSumNewRoll}}条</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">本色盘前：{{GoodsCodeNoSumOldRoll}}条</text>
				<text class="title" style="width:200px;">本色实盘：{{GoodsCodeNoSumNewRoll}}条</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">本缸盘前：{{CrockNoSumOldRoll}}条</text>
				<text class="title" style="width:200px;">本缸实盘：{{CrockNoSumNewRoll}}条</text>
			</u-form-item>
		</u-form>
		<view class="u-demo-area">
			<u-toast ref="uToast"></u-toast>
			<wyb-table ref="table" :headers="headersMaster" :contents="GoodsDetailList" />
			<!--	<wyb-table ref="table" :headers="headersDetail" :contents="GoodsCrockNoDataList"  height="600rpx" /> !-->
		</view>
		<!-- 		<u-action-sheet :list="InDetailList" v-model="actionSheetShow"  @click="actionSheetCallback"></u-action-sheet> -->

		<view class="submitView">
			<u-button type="primary" style="width:100px;" class="commitBtn" :ripple="true" ripple-bg-color="#909399"
				@click="commitBtnFun">
				{{CommitType ? '消审' : '审核'}}
			</u-button>
		</view>
	</view>
</template>

<script>
	import util, {
		parFabricGoodsBarCode2D,
		parYarnGoodsBarCode2D,
		playSuccessAudio,
		playErrorAudio
	} from '../../common/util';
	import wybTable from '@/components/wyb-table/wyb-table.vue';
	//let that = '';
	export default {
		data() {
			return {
				borderColor: '#e4e7ed',
				align: 'center',
				index: 0,
				CommitType: '',
				CommitProcName: '',
				actionSheetShow: false,
				QRBarCode: '',
				GoodsCheckBillID: 0,
				GoodsCheckBillNo: '',
				GoodsCheckBillDate: '',
				GoodsCheckBillMasterNo: '',
				StoreNameID: '',
				StoreName: '',
				StoreStationID: '',
				StoreStationNo: '',
				StoreStationName: '',
				CustomerName: '',
				FabricGoodsNo: '',
				FabricGoodsName: '',
				GoodsCodeNo: '',
				GoodsCodeName: '',
				CrockNo: '',
				GoodsBillNo: '',
				GoodsQty: 0,
				StoreStationNameFocus: false,
				BillSumOldRoll: 0,
				BillSumNewRoll: 0,
				GoodsCodeNoSumOldRoll: 0,
				GoodsCodeNoSumNewRoll: 0,
				CrockNoSumOldRoll: 0,
				CrockNoSumNewRoll: 0,
				BarCodeDelStatus: false,
				GoodsDetailList: [],
				BillDataMessage: '',
				headersMaster: [{
					label: '成品编号',
					key: 'product_code'
				}, {
					label: '成品名称',
					key: 'product_name'
				}, {
					label: '成品色号',
					key: 'product_color_code'
				}, {
					label: '成品颜色',
					key: 'product_color_name'
				}, {
					label: '成品缸号',
					key: 'dyelot_number'
				}, {
					label: '盘前条数',
					key: 'roll'
				}, {
					label: '实盘条数',
					key: 'check_roll'
				}, {
					label: '盈亏条数',
					key: 'different_roll'
				}],
				scanReceiver: null,
				isPageActive: false, // 添加页面活动状态标志
			}
		},

		onLoad(e) {
			//that = this;
			if (e.billid) {
				this.GoodsCheckBillID = e.billid;
				this.GoodsCheckBillDetailData();
			}
			
    	// #ifdef APP-PLUS
			this.isPageActive = true;
			this.registerScanBroadcast();
			// #endif
		},
		onUnload() {
			// #ifdef APP-PLUS
			this.isPageActive = false;
			// this.unregisterBroadcast();
			// #endif
		},

		onHide() {
			// 页面隐藏时
			this.isPageActive = false;
		},

		onShow() {
			// 页面显示时
			this.isPageActive = true;
		},
		methods: {
			// 添加通用错误提示方法
			showError(message) {
				this.playError();
				uni.showModal({
					title: '提示',
					content: message,
					showCancel: false
				});
			},
    	// 注册扫码广播接收器
			registerScanBroadcast() {
				try {
					const main = plus.android.runtimeMainActivity();

					// 先配置扫码枪广播设置
					try {
						const Intent = plus.android.importClass("android.content.Intent");
						const intent = new Intent("com.android.scanner.service_settings");
						intent.putExtra(
							"action_barcode_broadcast",
							"com.android.server.scannerservice.broadcast"
						);
						intent.putExtra("key_barcode_broadcast", "scannerdata");
						main.sendBroadcast(intent);
					} catch (error) {
						console.error("配置扫码枪广播失败：", error);
					}

					// 注册广播接收器
					const IntentFilter = plus.android.importClass(
						"android.content.IntentFilter"
					);
					const filter = new IntentFilter();
					filter.addAction("com.android.server.scannerservice.broadcast");
					console.log("添加广播action完成");

					const receiver = plus.android.implements(
						"io.dcloud.feature.internal.reflect.BroadcastReceiver",
						{
							onReceive: (context, intent) => {
								// 只有当页面活动时才处理广播
								if (!this.isPageActive) return;
								
								try {
									const scanResult = intent.getStringExtra("scannerdata");
									console.log("配布单详情-扫码结果:", scanResult);
									if (scanResult) {
										this.QRBarCode = scanResult;
										this.$nextTick(() => {
											this.GoodsCheckBillDetailScan();
										});
									}
								} catch (error) {
									console.error("处理广播数据时出错：", error);
								}
							},
						}
					);

					// 注册广播接收器
					main.registerReceiver(receiver, filter);
					this.scanReceiver = receiver;
					console.log("扫码广播注册成功，等待扫码...");
				} catch (error) {
					console.error("注册扫码广播失败：", error);
					console.error("错误详情：", error.message);
					console.error("错误堆栈：", error.stack);
				}
			},

			// 注销扫码广播接收器
			unregisterScanBroadcast() {
				if (this.scanReceiver) {
					try {
						const main = plus.android.runtimeMainActivity();
						main.unregisterReceiver(this.scanReceiver);
						this.scanReceiver = null;
						console.log("扫码广播注销成功");
					} catch (error) {
						console.error("注销扫码广播失败：", error);
					}
				}
			},
			playSuccess() {
				util.playSuccessAudio();
			},
			playError() {
				util.playErrorAudio();
			},

			BarCodeDelChange: function() {
				this.BarCodeDelStatus = !this.BarCodeDelStatus;
			},

			GoodsCheckBillDetailScan() {
				if (this.StoreNameID == 0 && this.GoodsCheckBillNo == '') {
					this.QRBarCode = '';
					this.showError('请先新增单据');
					return;
				}
				console.log("this.QRBarCode ---->>" + this.QRBarCode);
				
				var aQRBarCode = '';
				var aBarCode = '';
				if (this.QRBarCode.startsWith('66^')>0){
					aQRBarCode = this.QRBarCode
				}else {
					aBarCode = this.QRBarCode;
				}
				
				if (aQRBarCode == '' && aBarCode == ''){
					this.QRBarCode = '';
					this.aBarCode = ''
					this.showError('请扫描二维码或者条码');
					return;
				}
				
				let aBarCodeDelStatus = 1
				if (this.BarCodeDelStatus) {
					aBarCodeDelStatus = 3;
				};
				
				util.request({
					url: '/product/productCheckOrder/updateProductCheckOrder',
					method: 'put',
					data: {
						'arrange_type': aBarCodeDelStatus,
						'id': parseInt(this.GoodsCheckBillID),
						'bar_code': aBarCode,
						'qr_code': aQRBarCode,
					},
					success: (res) => {
						console.log("-->>--" + JSON.stringify(res.data));
						if (res.data.code == '0'){
							let aResultData = res.data.data;
							this.FabricGoodsNo = aResultData.product_code;
							this.FabricGoodsName = aResultData.product_name;
							this.GoodsCodeNo = aResultData.product_color_code;
							this.GoodsCodeName = aResultData.product_color_name;
							this.CrockNo = aResultData.dyelot_number;
							this.GoodsBillNo = aResultData.volume_number;
							this.GoodsQty = aResultData.weight / 10000;
							this.BillSumOldRoll = aResultData.warehouse_bin_check_before_roll/100;
							this.BillSumNewRoll = aResultData.warehouse_bin_check_roll/100;
							this.GoodsCodeNoSumOldRoll = aResultData.color_check_before_roll/100;
							this.GoodsCodeNoSumNewRoll = aResultData.color_check_roll/100;
							this.CrockNoSumOldRoll = aResultData.dye_check_before_roll/100;
							this.CrockNoSumNewRoll = aResultData.dye_check_roll/100;
							
							this.playSuccess();
							this.GoodsCheckBillDetailData();
							this.BillDataMessage = res.data.msg;
							this.QRBarCode = '';
							
						}
						else {
							this.showError(res.data.msg);
							this.QRBarCode = '';
							
						}
					},
					fail: (error) => {
						this.showError('连接服务器出错，请检查后台服务是否启动！');
						console.log('error',error)
					},
				})
			},

			GoodsCheckBillDetailData: function() {
				console.log("--aaa->>" + this.GoodsCheckBillID);
				console.log("--aaa->>" + this.GoodsCheckBillID);
				util.request({
					url: '/product/productCheckOrder/getProductCheckOrder',
					method: 'get',
					data: {
						'id': parseInt(this.GoodsCheckBillID),
					},
					success: (res) => {
						console.log("-->>--" + JSON.stringify(res.data.data));
						this.GoodsDetailList = [];
						this.GoodsCheckBillNo = res.data.data.order_no;
						this.StoreName = res.data.data.warehouse_name
						this.StoreStationName = res.data.data.warehouse_bin_name;
						this.GoodsCheckBillDate = this.$u.timeFormat(res.data.data.check_time, 'yyyy-mm-dd')
						/* this.BillSumOldRoll = res.data.data.item_data.roll;
						this.BillSumNewRoll = res.data.data.item_data.check_roll; */
						var aResultData = res.data.data.item_data;
						this.BillSumOldRoll = 0;
						this.BillSumNewRoll = 0;
						for (var i = 0; i < aResultData.length; i++) {
							aResultData[i].roll = aResultData[i].roll / 100;
							aResultData[i].check_roll = aResultData[i].check_roll / 100;
							aResultData[i].different_roll = aResultData[i].different_roll / 100;
							
							this.BillSumOldRoll = this.BillSumOldRoll + aResultData[i].roll;
							this.BillSumNewRoll = this.BillSumNewRoll + aResultData[i].check_roll;
						};
						
						this.GoodsDetailList = aResultData;
					},
				})
			},

			// 审核按钮方法
			commitBtnFun: function() {
				if (this.BillMasterID <= 0) {
					this.showError('当前单据未提交，不能审核或消审！');
					return;
				}

				var aCommitRecallName = '审核';
				if (this.CommitType == '') {
					aCommitRecallName = '审核';
				} else {
					aCommitRecallName = '消审';
				}

				// if (this.CommitProcName == '') {
				// 	this.playError();
				// 	this.BillDataMessage = '当前' + aCommitRecallName + '操作配置有误，不能审核！';
				// 	return;
				// }
				console.log('GoodsCheckBillID',this.GoodsCheckBillID)
				util.request({
					url: '/product/productCheckOrder/updateProductCheckOrderAuditStatusPass',
					method: 'put',
					data: {
						'id': parseInt(this.GoodsCheckBillID),
					},
					success: (res) => {
						console.log('审核',res)
						if (res.data.code == '0') {
							var aResultData = JSON.parse(res.data.data);
							console.log('aResultData',aResultData)
							if (aResultData.code == '0' && aResultData.msg ==
								'success') {
								this.playSuccess();
								this.BillDataMessage = aCommitRecallName + "成功！";

								if (aCommitRecallName == '审核') {
									this.CommitType = '已审核'
								} else {
									this.CommitType = ''
								}
							} else {
								this.showError(aCommitRecallName + '出错！' + aResultData.BillDataMessage);
								return;
							}
						} else {
							this.showError(aCommitRecallName + '出错，' + res.data.msg);
							return;
						}
					},
					fail: (error) => {
						this.showError('连接服务器出错，请检查后台服务是否启动！');
					},
				})
			},

		}
	}
</script>

<style>
	page {
		background-color: #F8F8F8;
		padding-bottom: 260rpx;
	}

	.u-radio {
		width: 200rpx !important;
	}

	.submitView {
		width: 100%;
		padding: 16rpx 0 26rpx;
		background-color: #FFFFFF;
		position: fixed;
		bottom: 0;
		left: 0;
		border-top: 1rpx solid #f1f1f1;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 100;
	}

	.submitBtn {
		width: 666rpx;
	}

	.productBox {
		background-color: #FFFFFF;
		margin-top: 32rpx;
		padding: 26rpx 0;
	}

	.tjcpName {
		width: 686rpx;
		height: 40rpx;
		font-size: 16px;
		font-weight: bold;
		border-left: 6rpx solid #007AFF;
		padding-left: 12rpx;
		margin-left: 26rpx;
		margin-top: 26rpx;
	}

	.cpInput {
		width: 150rpx !important;
		margin-right: 12rpx;
	}

	.cpInput>input {
		box-sizing: border-box;
		border: 1rpx solid #DDDDDD;
		width: 100%;
		height: 60rpx;
		border-radius: 10rpx;
		padding: 0 10rpx;
	}

	.cpInput1 {
		width: 200rpx !important;
		margin-right: 12rpx;
	}

	.cpInput1>input {
		box-sizing: border-box;
		border: 1rpx solid #DDDDDD;
		width: 100%;
		height: 60rpx;
		border-radius: 10rpx;
		padding: 0 10rpx;
	}

	.clearIcon {
		position: absolute;
		right: 6rpx;
		top: 6rpx;
	}

	.greenPrice {
		font-size: 16px;
		color: #19BE6B !important;
		font-weight: bold;
	}

	.disFlex {
		display: flex;
		align-items: center;
		margin-bottom: 8rpx;
	}

	.inputName {
		color: #ADADAD;
		font-size: 16px;
	}

	.addHKQS {
		display: flex;
		align-items: center;
		padding: 16rpx 26rpx;
		font-size: 15px;
		font-weight: bold;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>
