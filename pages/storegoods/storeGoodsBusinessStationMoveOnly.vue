<template>
	<view class="wrap">
		<u-form ref="uForm">
			<view @click="pickerSelectFun('仓库名称')" class="flex-white-plr26 ptb20 bdb_f5 first">
				<text class="mr26">仓库名称<text class="redXingh">*</text></text>
				<view :class="StoreName ? '' : 'cBlack'"> {{StoreName ? StoreName : '请选择'}}
					<u-icon class="ml26" name="arrow-right" size="40" color="#888888"></u-icon>
				</view>
			</view>
			<u-form-item label-width="150" label="调出仓位:"
					:class="{'input-highlight': shouldHighlightFrom}"
			>
				<input type="text" 
					v-model="FromStoreStationNo" 
					maxlength="-1" 
					style="width:100px;" 
					@confirm="GetFromStoreStationName" />
				<text style="width:100px;">{{FromStoreStationName}}</text>
				<checkbox-group @change="AllCrockNoCheckChange">
					<checkbox ref="checkBoxRef" :checked="AllCrockNoScanStatus">整缸</checkbox>
				</checkbox-group>
			</u-form-item>
			<u-form-item label-width="150" label="调至仓位:"
					:class="{'input-highlight': shouldHighlightTo}"
			
			>
				<input type="text" 
					v-model="ToStoreStationNo" 
					maxlength="-1" 
					style="width:100px;" 
					@confirm="GetToStoreStationName" />
				<text style="width:100px;">{{ToStoreStationName}}</text>
				<checkbox-group @change="AllStoreStationChange">
					<checkbox ref="checkBoxRef" :checked="AllStoreStationScanStatus">整架</checkbox>
				</checkbox-group>
			</u-form-item>
			<u-form-item label-width="150" label="条码资料:"
					:class="{'input-highlight': shouldHighlightQR}"
			
			>
				<input type="text" 
					v-model="QRBarCode" 
					maxlength="-1"
					style="width:150px;" 
					@confirm="GoodsStoreStationMoveScan" />
			</u-form-item>
			<u-form-item>
				<text class="title">{{BillDataMessage}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">成品编号：{{FabricGoodsNo}}</text>
				<text class="title" style="width:200px;">成品重量：{{GoodsQty}}KG</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">成品色号：{{GoodsCodeNo}}</text>
				<text class="title" style="width:200px;">成品颜色：{{GoodsCodeName}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">成品缸号：{{CrockNo}}</text>
				<text class="title" style="width:200px;">缸号卷号：{{GoodsBillNo}}</text>
				
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">本架条数：{{StoreStationSumRoll}}条</text>
				<text class="title" style="width:200px;">本架数量：{{StoreStationSumQty}}KG</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">本色条数：{{GoodsCodeSumRoll}}条</text>
				<text class="title" style="width:200px;">本色数量：{{GoodsCodeSumQty}}KG</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">本缸条数：{{CrockNoSumRoll}}条</text>
				<text class="title" style="width:200px;">本缸数量：{{CrockNoSumQty}}KG</text>
			</u-form-item>
		</u-form>
		<view class="u-demo-area">
			<u-toast ref="uToast"></u-toast>
			<wyb-table ref="table" :headers="headersMaster" :contents="GoodsDetailList"/>
		</view>
		<u-select v-model="selectShow" :list="selectList" @confirm="selectConfirmFun"></u-select>
	</view>
</template>

<script>
	let that = '';
	import util, {
		parFabricGoodsBarCode2D,
		parYarnGoodsBarCode2D,
		playSuccessAudio,
		playErrorAudio
	} from '../../common/util';
	import wybTable from '@/components/wyb-table/wyb-table.vue';
	export default {
		data() {
			return {
				borderColor: '#e4e7ed',
				align: 'center',
				index: 0,
				selectShow: false,
				selectList: [],
				selectType: '',
				actionSheetShow: false,
				QRBarCode: '',
				AllCrockNoScanStatus: false,
				AllStoreStationScanStatus: false,
				FromStoreStationID: 0,
				FromStoreStationNo: '',
				FromStoreStationName: '',
				ToStoreStationID: 0,
				ToStoreStationNo: '',
				ToStoreStationName: '',
				StoreNameID: '',
				StoreName: '',
				FabricGoodsNo: '',
				FabricGoodsName: '',
				GoodsCodeNo: '',
				GoodsCodeName: '',
				CrockNo: '',
				GoodsBillNo: '',
				GoodsQty: 0,
				FromStoreStationFocus: false,
				QRBarCodeFocus: false,				
				ToStoreStationFocus: true,
				StoreStationSumRoll: 0,
				StoreStationSumQty: 0,
				GoodsCodeSumRoll: 0,
				GoodsCodeSumQty: 0,
				CrockNoSumRoll: 0,
				CrockNoSumQty: 0,
				StoreNameDataList: [],
				GoodsDetailList: [],
				BillDataMessage: '',
				headersMaster: [{
					label: '成品编号',
					key: 'product_code'
				}, {
					label: '成品名称',
					key: 'product_name'
				},{
					label: '成品色号',
					key: 'product_color_code'
				}, {
					label: '成品颜色',
					key: 'product_color_name'
				}, {
					label: '成品缸号',
					key: 'dyelot_number'
				}, {
					label: '条数',
					key: 'roll'
				}, {
					label: '数量',
					key: 'weight'
				}],
				scanReceiver: null,
				isPageActive: false, // 添加页面活动状态标志
			}
		},
		onLoad(e) {
			that = this;
			setTimeout(() => {
				this.getStoreNameData();
			}, 500);
			
			if (getApp().globalData.StoreTypeNo.toUpperCase() == 'STOREFABRICGOODS') {
				this.StoreNameID = getApp().globalData.StoreNameID;
				this.StoreName = getApp().globalData.StoreName;
			};
			// #ifdef APP-PLUS
			this.isPageActive = true;
			this.registerScanBroadcast();
			// #endif
	/* 		uni.$on('bjdKehuBindFun', that.bjdKehuBindFun)
			uni.$on('chanpinBindFun', that.chanpinBindFun)
			uni.$on('bjdLxrBindFun', that.bjdLxrBindFun)
			uni.$on('shangjiBindFun', that.shangjiBindFun) */
		},
		onUnload() {
			// #ifdef APP-PLUS
			this.isPageActive = false;
			// this.unregisterBroadcast();
			// #endif
		},

		onHide() {
			// 页面隐藏时
			this.isPageActive = false;
		},

		onShow() {
			// 页面显示时
			this.isPageActive = true;
		},
	/* 	onBackPress() {
			uni.$off('bjdKehuBindFun', that.bjdKehuBindFun)
			uni.$off('chanpinBindFun', that.chanpinBindFun)
			uni.$off('bjdLxrBindFun', that.bjdLxrBindFun)
			uni.$off('shangjiBindFun', that.shangjiBindFun)
		}, */
		computed: {
			shouldHighlightFrom() {
				return !this.FromStoreStationNo;
			},
			shouldHighlightTo() {
				return this.FromStoreStationNo && !this.ToStoreStationNo;
			},
			shouldHighlightQR() {
				return this.FromStoreStationNo && this.ToStoreStationNo && !this.QRBarCode;
			}
		},
		methods: {
			playSuccess() {
				util.playSuccessAudio();
			},
			playError() {
				util.playErrorAudio();
			},
			
			AllCrockNoCheckChange:function () {
				this.AllCrockNoScanStatus = !this.AllCrockNoScanStatus;
				this.AllStoreStationScanStatus = false;
			},
			
			AllStoreStationChange:function () {
				this.AllStoreStationScanStatus = !this.AllStoreStationScanStatus;
				this.AllCrockNoScanStatus = false;
			},
			
			// 展示相应数据选择框
			pickerSelectFun: function(str) {
				that.selectList = [];
				if (str == '仓库名称') {
					that.selectList = this.StoreNameDataList;
				}
				
				that.selectShow = true;
				that.selectType = str; 
			},
			
			scanFun: function() {
				uni.scanCode({
					success(res) {
						that.code = res.result;
					}
				})
			},
			
			// 选择框选中事件
			selectConfirmFun: function(e) {
				if (that.selectType == '仓库名称') {
					that.StoreNameID = e[0].value;
					that.StoreName = e[0].label;
				}
				
			/* 	console.log(JSON.stringify(that.selectList)); */
				that.selectShow = true;
				/* that.selectType = str; */
			},
			
    	// 注册扫码广播接收器
			registerScanBroadcast() {
				try {
					const main = plus.android.runtimeMainActivity();

					// 先配置扫码枪广播设置
					try {
						const Intent = plus.android.importClass("android.content.Intent");
						const intent = new Intent("com.android.scanner.service_settings");
						intent.putExtra(
							"action_barcode_broadcast",
							"com.android.server.scannerservice.broadcast"
						);
						intent.putExtra("key_barcode_broadcast", "scannerdata");
						main.sendBroadcast(intent);
					} catch (error) {
						console.error("配置扫码枪广播失败：", error);
					}

					// 注册广播接收器
					const IntentFilter = plus.android.importClass(
						"android.content.IntentFilter"
					);
					const filter = new IntentFilter();
					filter.addAction("com.android.server.scannerservice.broadcast");
					console.log("添加广播action完成");

					const receiver = plus.android.implements(
						"io.dcloud.feature.internal.reflect.BroadcastReceiver",
						{
							onReceive: (context, intent) => {
								// 只有当页面活动时才处理广播
								if (!this.isPageActive) return;
								
								try {
									const scanResult = intent.getStringExtra("scannerdata");
									console.log("配布单详情-扫码结果:", scanResult);
									if(!this.StoreName){
        						this.showError("请先选择仓库名称");
										return 
									}
									if (scanResult) {
										if(!this.FromStoreStationNo){
											this.FromStoreStationNo = scanResult
											this.$nextTick(() => {
												this.GetFromStoreStationName();
											});
											return
										}else if(!this.ToStoreStationNo){
											this.ToStoreStationNo = scanResult;
											this.$nextTick(() => {
												this.GetToStoreStationName();
											});
											return

										}else if(!this.QRBarCode) {
											this.QRBarCode = scanResult;
											this.$nextTick(() => {
												this.GoodsStoreStationMoveScan();
											});
											return
										}
									}
								} catch (error) {
									this.showError("处理扫码数据时出错");
									console.error("处理广播数据时出错：", error);
								}
							},
						}
					);

					// 注册广播接收器
					main.registerReceiver(receiver, filter);
					this.scanReceiver = receiver;
					console.log("扫码广播注册成功，等待扫码...");
				} catch (error) {
					console.error("注册扫码广播失败：", error);
					console.error("错误详情：", error.message);
					console.error("错误堆栈：", error.stack);
				}
			},

			// 注销扫码广播接收器
			unregisterScanBroadcast() {
				if (this.scanReceiver) {
					try {
						const main = plus.android.runtimeMainActivity();
						main.unregisterReceiver(this.scanReceiver);
						this.scanReceiver = null;
						console.log("扫码广播注销成功");
					} catch (error) {
						console.error("注销扫码广播失败：", error);
					}
				}
			},
			// 添加一个通用的错误提示方法
			showError(message) {
				this.playError();
				uni.showModal({
					title: '提示',
					content: message,
					showCancel: false
				});
			},
			getStoreNameData() {
				util.request({
					url: '/warehouse/physicalWarehouse/getPhysicalWarehouseDropdownList',
					method: 'GET',
					data: {
						'warehouse_type_id': 103,
					},
					success: (res) => {
						console.log("getStoreNameData" + res);
						if (res.data.code == '0' && res.data.msg == 'success'){
							var aResultData = res.data.data.list;
							for (var i = 0; i < aResultData.length; i++) {
								this.StoreNameDataList.push({
									value: aResultData[i].id,
									label: aResultData[i].name
								})
							}
						}
						else{
							this.StoreNameDataList = []
						}
					},
					fail: (error) => {
						this.showError('连接服务器出错，请检查后台服务是否启动！' + error.errMsg);
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},
			
			GetFromStoreStationName: function() {
				console.log("<<<>>>>>" + this.FromStoreStationNo + '<<<<>>>>');
				util.request({
					url: '/warehouse/warehouseBin/getWarehouseBin',
					method: 'GET',
					data: {
						'qr_code': this.FromStoreStationNo,
					},
					success: (res) => {
						console.log('GetFromStoreStationName',res)
						if (res.data.msg == 'success'){
							if(res.data.data.physical_warehouse_id !== this.StoreNameID) {
								this.showError('该仓位不属于当前仓库')
								return
							}
							this.FromStoreStationID = res.data.data.id;
							this.FromStoreStationNo = res.data.data.code;
							this.FromStoreStationName = res.data.data.name;
						}
						
						this.ToStoreStationFocus = false;
						this.$nextTick(() => {
							this.ToStoreStationFocus = true;
						});
					},
					fail: (error) => {
						this.showError('连接服务器出错，请检查后台服务是否启动！' + error.errMsg);
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},
			
			GetToStoreStationName: function() {
				console.log("<<<>>>>>" + this.ToStoreStationNo + '<<<<>>>>');
				util.request({
					url: '/warehouse/warehouseBin/getWarehouseBin',
					method: 'GET',
					data: {
						'qr_code': this.ToStoreStationNo,
					},
					success: (res) => {
						if (res.data.msg == 'success'){
							if(res.data.data.physical_warehouse_id !== this.StoreNameID) {
								this.showError('该仓位不属于当前仓库')
								return
							}
							this.ToStoreStationID = res.data.data.id;
							this.ToStoreStationNo = res.data.data.code;
							this.ToStoreStationName = res.data.data.name;
						}
						
						this.QRBarCodeFocus = false;
						this.$nextTick(() => {
							this.QRBarCodeFocus = true;
						});
					},
					fail: (error) => {
						this.showError('连接服务器出错，请检查后台服务是否启动！' + error.errMsg);
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},
			
			GoodsStoreStationMoveScan() {
				if (this.StoreNameID == 0) {
					this.showError('请先选择仓库名称');
					return;
				}
				
				if (this.ToStoreStationName == '') {
					this.showError('请先输入调至仓库');
					return;
				}
				
				let aAllStoreStationScanStatus = 0;
				if (this.AllStoreStationScanStatus){
					aAllStoreStationScanStatus = '1';
				};
				
				let aAllCrockNoScanStatus = 0;
				if (this.AllCrockNoScanStatus){
					aAllCrockNoScanStatus = '1';
				};
				
				if (this.AllStoreStationScanStatus && this.FromStoreStationName == ''){
					this.showError('选择整架调仓位，必须要先输入调出仓位');
					return;
				};
				
				if (this.aAllCrockNoScanStatus && this.FromStoreStationName == ''){
					this.showError('选择整缸调仓位，必须要先输入调出仓位');
					return;
				};
				
				let aTypeID = 1;
				if (this.aAllCrockNoScanStatus){
					aTypeID = 2
				} else if (this.AllStoreStationScanStatus){
					aTypeID = 3
				}
			
				var aQRBarCode = '';
				var aBarCode = '';
				if (this.QRBarCode.startsWith('66^')>0){
					aQRBarCode = this.QRBarCode
				}else {
					aBarCode = this.QRBarCode;
				}
				
				if (aQRBarCode == ''){
					this.QRBarCode = '';
					this.QRBarCodeFocus = false;
					this.$nextTick(() => {
						this.QRBarCodeFocus = true;						
					});
					this.showError('请扫描二维码');
					return;
				}
				
				util.request({
					url: '/product/stockProduct/move',
					method: 'POST',
					data: {
						'qr_code': aQRBarCode,
						'source_bin_id': this.FromStoreStationID,
						'target_bin_id': this.ToStoreStationID,
						'type': aTypeID,
					},
					success: (res) => {
						console.log("-->>--" + JSON.stringify(res.data.data));
						if (res.data.code == '0'){
							let aResultData = res.data.data;
							this.FabricGoodsNo = aResultData.product_code;
							this.FabricGoodsName = aResultData.product_name;
							this.GoodsCodeNo = aResultData.product_color_code;
							this.GoodsCodeName = aResultData.product_color_name;
							this.CrockNo = aResultData.dyelot_number;
							this.GoodsBillNo = aResultData.volume_number;
							this.GoodsQty = aResultData.weight / 10000;
							this.StoreStationSumRoll = aResultData.warehouse_bin_roll/100;
							this.StoreStationSumQty = aResultData.warehouse_bin_weight/10000;
							this.GoodsCodeSumRoll = aResultData.color_roll/100;
							this.GoodsCodeSumQty = aResultData.color_weight/10000;
							this.CrockNoSumRoll = aResultData.dye_roll/100;
							this.CrockNoSumQty = aResultData.dye_weight/10000;
							
							this.playSuccess();
							this.GoodsStoreStationMoveData();
							this.BillDataMessage = res.data.msg;
							this.QRBarCode = '';
							this.QRBarCodeFocus = false;
							this.$nextTick(() => {
								this.QRBarCodeFocus = true;
							});
						}
						else {
							this.playError();
							this.BillDataMessage = res.data.msg;
							this.QRBarCode = '';
							this.QRBarCodeFocus = false;
							this.$nextTick(() => {
								this.QRBarCodeFocus = true;						
							});
						}
					},
				})
			},

			GoodsStoreStationMoveData: function() {
				util.request({
					url: '/product/stockProduct/getStockProductDyelotNumberList',
					method: 'GET',
					data: {
						'warehouse_bin_id': this.ToStoreStationID,
					},
					success: (res) => {
			/* 			console.log("-->>--" + JSON.stringify(res.data.data)); */
						this.GoodsDetailList = [];
						var aResultData = res.data.data.list;
						this.StoreStationSumRoll = 0;
						this.StoreStationSumQty = 0;
						for (var i = 0; i < aResultData.length; i++) {
							console.log("---aResultData[i].weight>>" + aResultData[i].weight);
							aResultData[i].roll = aResultData[i].roll / 100;
							aResultData[i].weight = aResultData[i].weight / 10000;
							
							this.StoreStationSumRoll = this.StoreStationSumRoll + aResultData[i].roll;
							this.StoreStationSumQty = this.StoreStationSumQty + aResultData[i].weight;
						};
						
						this.GoodsDetailList = aResultData;
					},
				})
			},
			
		}
	}
</script>

<style>
	.first{
		box-sizing: border-box;
	}
.wrap{
	width: 100vw;
	overflow-x: hidden;
}
.input-highlight {
  border: 1px solid #ff0000 !important;
}
	page {
		background-color: #F8F8F8;
		padding-bottom: 260rpx;
	}

	.u-radio {
		width: 200rpx !important;
	}

	.submitView {
		width: 100%;
		padding: 16rpx 0 26rpx;
		background-color: #FFFFFF;
		position: fixed;
		bottom: 0;
		left: 0;
		border-top: 1rpx solid #f1f1f1;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 100;
	}

	.submitBtn {
		width: 666rpx;
	}

	.productBox {
		background-color: #FFFFFF;
		margin-top: 32rpx;
		padding: 26rpx 0;
	}

	.tjcpName {
		width: 686rpx;
		height: 40rpx;
		font-size: 16px;
		font-weight: bold;
		border-left: 6rpx solid #007AFF;
		padding-left: 12rpx;
		margin-left: 26rpx;
		margin-top: 26rpx;
	}

	.cpInput {
		width: 150rpx !important;
		margin-right: 12rpx;
	}

	.cpInput>input {
		box-sizing: border-box;
		border: 1rpx solid #DDDDDD;
		width: 100%;
		height: 60rpx;
		border-radius: 10rpx;
		padding: 0 10rpx;
	}

	.cpInput1 {
		width: 200rpx !important;
		margin-right: 12rpx;
	}

	.cpInput1>input {
		box-sizing: border-box;
		border: 1rpx solid #DDDDDD;
		width: 100%;
		height: 60rpx;
		border-radius: 10rpx;
		padding: 0 10rpx;
	}

	.clearIcon {
		position: absolute;
		right: 6rpx;
		top: 6rpx;
	}

	.greenPrice {
		font-size: 16px;
		color: #19BE6B !important;
		font-weight: bold;
	}

	.disFlex {
		display: flex;
		align-items: center;
		margin-bottom: 8rpx;
	}

	.inputName {
		color: #ADADAD;
		font-size: 16px;
	}

	.addHKQS {
		display: flex;
		align-items: center;
		padding: 16rpx 26rpx;
		font-size: 15px;
		font-weight: bold;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>
