<template>
	<view class="wrap">
		<u-form ref="uForm">
			<u-form-item>
				<text class="title" style="width:200px;">单号：{{GoodsStationMoveBillNo}}</text>
				<text class="title" style="width:200px;">日期：{{GoodsStationMoveBillDate}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">仓库名称：{{StoreName}}</text>
				<text class="title" style="width:200px;">仓位名称：{{ScanStoreStationName}}</text>
			</u-form-item>
			<u-form-item>
				<text class="mr26" >移出仓位<text class="redXingh">*</text></text>
				<input type="text" v-model="FromStoreStationNo" :focus="FromStoreStationFocus" style="width:100px;" @confirm="GetFromStoreStationName" />
				<text class="title" style="width:120px;">{{FromStoreStationName}}</text>
				<checkbox-group  @change="AllCrockNoCheckChange">
					<checkbox ref="checkBoxRef" :checked="AllCrockNoScanStatus">整缸</checkbox>
				</checkbox-group>
			</u-form-item>
			<u-form-item>
				<text class="mr26" >移至仓位<text class="redXingh">*</text></text>
				<input type="text" v-model="StoreStationNo" :focus="ToStoreStationFocus"
					style="width:100px;" @confirm="GetStoreStationName" />
				<text class="title" style="width:120px;">{{StoreStationName}}</text>
				<checkbox-group  @change="AllStoreStationChange">
					<checkbox ref="checkBoxRef" :checked="AllStoreStationScanStatus">整架</checkbox>
				</checkbox-group>
			</u-form-item>
			<u-form-item label-width="150" label="条码资料:">
				<input type="text" v-model="QRBarCode" maxlength="-1" :focus="QRBarCodeFocus" style="width:220px;"
					@confirm="GoodsStationMoveBillDetailScan" />
				<checkbox-group  @change="QRBarCodeDelChange">
					<checkbox ref="checkBoxRef" :checked="QRBarCodeDelStatus">删除</checkbox>
				</checkbox-group>

			</u-form-item>
			<u-form-item>
				<text class="title">{{BillDataMessage}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">成品编号：{{FabricGoodsNo}}</text>
				<text class="title" style="width:200px;">成品重量：{{GoodsQty}}KG</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">成品色号：{{GoodsCodeNo}}</text>
				<text class="title" style="width:200px;">成品颜色：{{GoodsCodeName}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">成品缸号：{{CrockNo}}</text>
				<text class="title" style="width:200px;">缸号卷号：{{GoodsBillNo}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">本单条数：{{BillSumRoll}}条</text>
				<text class="title" style="width:200px;">本单重量：{{BillSumQty}}KG</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">本架条数：{{StoreStationSumRoll}}条</text>
				<text class="title" style="width:200px;">本架重量：{{StoreStationSumQty}}KG</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">本色条数：{{GoodsCodeSumRoll}}条</text>
				<text class="title" style="width:200px;">本色重量：{{GoodsCodeSumQty}}KG</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">本缸条数：{{CrockNoSumRoll}}条</text>
				<text class="title" style="width:200px;">本缸重量：{{CrockNoSumQty}}KG</text>
			</u-form-item>
		</u-form>
		<view class="u-demo-area">
			<u-toast ref="uToast"></u-toast>
			<wyb-table ref="table" :headers="headersMaster" :contents="GoodsDetailList"/>
		</view>
		<view class="submitView">
			<u-button type="primary" style="width:100px;" class="commitBtn"
				:ripple="true" ripple-bg-color="#909399" @click="commitBtnFun">
				{{CommitType ? '消审' : '审核'}}
			</u-button>
		</view>
	</view>
</template>

<script>
	import util, {
		parFabricGoodsBarCode2D,
		parYarnGoodsBarCode2D,
		playSuccessAudio,
		playErrorAudio
	} from '../../common/util';
	import wybTable from '@/components/wyb-table/wyb-table.vue';
	let that = '';
	export default {
		data() {
			return {
				borderColor: '#e4e7ed',
				align: 'center',
				index: 0,
				CommitType:'',
				CommitProcName: '',
				actionSheetShow: false,
				QRBarCode: '',
				GoodsStationMoveBillID: 0,
				GoodsStationMoveBillNo: '',
				GoodsStationMoveBillDate: '',
				GoodsStationMoveBillMasterNo: '',
				AllCrockNoScanStatus: false,
				AllStoreStationScanStatus: false,
				QRBarCodeDelStatus: false,
				StoreNameID: '',
				StoreName: '',
				FromStoreStationNo: '',
				FromStoreStationName: '',
				StoreStationNo: '',
				StoreStationName: '',
				ScanStoreStationName: '',
				CustomerName: '',
				FabricGoodsNo: '',
				FabricGoodsName: '',
				GoodsCodeNo: '',
				GoodsCodeName: '',
				CrockNo: '',
				GoodsBillNo: '',
				GoodsQty: 0,
				FromStoreStationFocus: false,
				ToStoreStationFocus: true,
				QRBarCodeFocus: false,
				StoreStationSumRoll: 0,
				StoreStationSumQty: 0,
				GoodsCodeSumRoll: 0,
				GoodsCodeSumQty: 0,
				CrockNoSumRoll: 0,
				CrockNoSumQty: 0,
				BillSumRoll: 0,
				BillSumQty: 0,
				BarCodeDelStatus: false,
				GoodsDetailList: [],
				BillDataMessage: '',
				headersMaster: [{
					label: '成品编号',
					key: 'FabricGoodsNo'
				}, {
					label: '成品色号',
					key: 'GoodsCodeNo'
				}, {
					label: '成品颜色',
					key: 'GoodsCodeName'
				}, {
					label: '成品缸号',
					key: 'CrockNo'
				}, {
					label: '条数',
					key: 'Roll'
				},  {
					label: '重量',
					key: 'Qty'
				}],
			}
		},

		onLoad(e) {
			that = this;
			if (e.billid) {
				that.GoodsStationMoveBillID = e.billid;
			}
			this.GoodsStationMoveBillDetailData();
		},

		methods: {
			playSuccess() {
				util.playSuccessAudio();
			},
			playError() {
				util.playErrorAudio();
			},

			AllCrockNoCheckChange:function () {
				this.AllCrockNoScanStatus = !this.AllCrockNoScanStatus;
			},

			AllStoreStationChange:function () {
				this.AllStoreStationScanStatus = !this.AllStoreStationScanStatus;
			},

			BarCodeDelChange:function () {
				this.BarCodeDelStatus = !this.BarCodeDelStatus;
			},

			GetStoreStationName: function() {
				if (this.StoreName == '') {
					this.playError();
					this.BillDataMessage = '仓库名称不能为空，请先输入仓库名称！';
					return;
				}
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetStoreNameStoreStationName',
							params: [{
								name: 'SID',
								value: this.StoreNameID
							}, {
								name: 'No',
								value: this.StoreStationNo
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							this.playSuccess();
							var aResultData = res.data.data;
							for (var i = 0; i < aResultData.length; i++) {
								this.StoreStationName = aResultData[i].StoreStationName;
							};
							this.QRBarCodeFocus = false;
							this.$nextTick(() => {
								this.QRBarCodeFocus = true;
							});
						} else {
							this.playError();
							this.StoreStationName = "",
							this.ToStoreStationFocus = false;
							this.$nextTick(() => {
								this.ToStoreStationFocus = true;
							});
							this.BillDataMessage = '不存在当前的仓位资料，请确认！';
						}
					},
					fail: (error) => {
						this.playError();
						this.BillDataMessage = '连接服务器出错，请检查后台服务是否启动！' + res.data.msg;
					},
				})
			},

			GetFromStoreStationName: function() {
				if (this.StoreName == '') {
					this.playError();
					this.BillDataMessage = '仓库名称不能为空，请先输入仓库名称！';
					return;
				}
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetStoreNameStoreStationName',
							params: [{
								name: 'SID',
								value: this.StoreNameID
							}, {
								name: 'No',
								value: this.FromStoreStationNo
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							this.playSuccess();
							var aResultData = res.data.data;
							for (var i = 0; i < aResultData.length; i++) {
								this.FromStoreStationName = aResultData[i].StoreStationName;
							};
							this.ToStoreStationFocus = false;
							this.$nextTick(() => {
								this.ToStoreStationFocus = true;
							});
						} else {
							this.playError();
							this.FromStoreStationName = "",
							this.FromStoreStationFocus = false;
							this.$nextTick(() => {
								this.FromStoreStationFocus = true;
							});
							this.BillDataMessage = '不存在当前的仓位资料，请确认！';
						}
					},
					fail: (error) => {
						this.playError();
						this.BillDataMessage = '连接服务器出错，请检查后台服务是否启动！' + res.data.msg;
					},
				})
			},

			GoodsStationMoveBillDetailScan() {
				if (this.StoreNameID == 0 && this.GoodsStationMoveBillNo == '') {
					this.playError();
					this.BillDataMessage = '请先新增单据';
					return;
				}
				console.log("this.QRBarCode ---->>" + this.QRBarCode);

				let aBarCodeDelStatus = '0';
				if (this.BarCodeDelStatus){
					aBarCodeDelStatus = '1';
				};

				let aAllCrockNoScanStatus = '0';
				if (this.AllCrockNoScanStatus){
					aAllCrockNoScanStatus = '1';
				};

				let aAllStoreStationScanStatus = '0'
				if (this.AllStoreStationScanStatus){
					aAllStoreStationScanStatus = '1';
				};

				uni.request({
					url: util.apiurl + 'rest/db/storedproc',
					data: {
						token: getApp().globalData.Token,
						data: {
							db_name: getApp().globalData.AppDBName,
							proc_name: 'Usp_APP_StoreGoodsBusinessStationMoveBillDetail',
							method: 'open_proc',
							params: [{
									name: '@BillMasterID',
									value: this.GoodsStationMoveBillID
								}, {
									name: '@QRBarCode',
									value: this.QRBarCode
								}, {
									name: '@FromStoreStationName',
									value: this.FromStoreStationName
								}, {
									name: '@ToStoreStationName',
									value: this.StoreStationName
								}, {
									name: '@StoreNameID',
									value: this.StoreNameID
								}, {
									name: '@AllStoreStationScanStatus',
									value: aAllStoreStationScanStatus
								}, {
									name: '@AllCrockNoScanStatus',
									value: aAllCrockNoScanStatus
								}, {
									name: '@BarCodeDelStatus',
									value: aBarCodeDelStatus
								}, {
									name: '@UserName',
									value: getApp().globalData.UserName
								}
							]
						},
					},
					success: (res) => {
						if (res.data.status == 0) {
							console.log(res.data.data.toString());
							var aResultData = JSON.parse(res.data.data);
							if (aResultData.BillDataStatus == '0' && aResultData.BillDataMessage == 'SUCCESS') {
								this.playSuccess();
								this.FabricGoodsNo = aResultData.FabricGoodsNo;
								this.FabricGoodsName = aResultData.FabricGoodsName;
								this.GoodsCodeNo = aResultData.GoodsCodeNo;
								this.GoodsCodeName = aResultData.GoodsCodeName;
								this.CrockNo = aResultData.CrockNo;
								this.GoodsBillNo = aResultData.GoodsBillNo;
								this.GoodsQty = aResultData.GoodsQty;
								this.GoodsCodeSumRoll = parseFloat(aResultData.GoodsCodeSumRoll);
								this.GoodsCodeSumQty = parseFloat(aResultData.GoodsCodeSumQty);
								this.StoreStationSumRoll = parseFloat(aResultData.StoreStationSumRoll);
								this.StoreStationSumQty = parseFloat(aResultData.StoreStationSumQty);
								this.CrockNoSumRoll = parseFloat(aResultData.CrockNoSumRoll);
								this.CrockNoSumQty = parseFloat(aResultData.CrockNoSumQty);
								this.BillSumRoll = parseFloat(aResultData.BillSumRoll);
								this.BillSumQty = parseFloat(aResultData.BillSumQty);
								this.QRBarCode = '';
								this.BillDataMessage = '扫描成功！';
								this.GoodsStationMoveBillDetailData();
								this.QRBarCodeFocus = false;
								this.$nextTick(() => {
									this.QRBarCodeFocus = true;
								});
							} else {
								this.playError();
								this.BillDataMessage = '移仓扫描出错，' + aResultData.BillDataMessage;
								this.QRBarCode = '';
								this.QRBarCodeFocus = false;
								this.$nextTick(() => {
									this.QRBarCodeFocus = true;
								});
								return;
							}

						} else {
							this.playError();
							this.QRBarCode = '';
							this.BillDataMessage = '移仓扫描出错，' + res.data.msg;
							this.QRBarCodeFocus = false;
							this.$nextTick(() => {
								this.QRBarCodeFocus = true;
							});
							return;
						}
					},
					fail: (error) => {
						this.playError();
						this.QRBarCode = '';
						this.QRBarCodeFocus = false;
						this.$nextTick(() => {
							this.QRBarCodeFocus = true;
						});
						this.BillDataMessage = '移仓扫描出错，' + res.data.msg;
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},

			GoodsStationMoveBillDetailData: function() {
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetStoreGoodsBusinessStationMoveDetailDataSQL',
							params: [{
								name: 'BillID',
								value: this.GoodsStationMoveBillID
							}]
						},
					},
					success: (res) => {
						this.triggered = false;
						let data = res.data.data;
						console.log("---->" + JSON.stringify(res.data));
						if (res.data.status == 0 && res.data.count > 0) {
							var aResultData = res.data.data;
							this.GoodsDetailList = res.data.data;
							this.GoodsStationMoveBillID = aResultData[0].BillMasterID;
							this.GoodsStationMoveBillNo = aResultData[0].BillNo;
							this.GoodsStationMoveBillDate = aResultData[0].BillDate;
							this.StoreName = aResultData[0].StoreName;
							this.StoreNameID= aResultData[0].StoreNameID;
							this.ScanStoreStationName = aResultData[0].ScanStoreStationName;
							this.CommitType = aResultData[0].CommitType;
							this.CommitProcName = aResultData[0].CommitProcName;
							this.BillSumRoll = 0;
							this.BillSumRoll = 0;
							for (var i = 0; i < aResultData.length; i++) {
								if (parseFloat(aResultData[i].Roll) > 0) {
									this.BillSumRoll = this.BillSumRoll + aResultData[i].Roll;
								}

								if (parseFloat(aResultData[i].Qty) > 0) {
									this.BillSumQty = this.BillSumQty + aResultData[i].Qty;
								}
							};
							this.BillSumRoll = this.BillSumRoll.toFixed(2);
							this.BillSumQty = this.BillSumQty.toFixed(2);

						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.GoodsDetailList = [];
						} else {
							this.GoodsDetailList = [];
						}

					},
				})
			},

			// 审核按钮方法
			commitBtnFun: function() {
				if (this.BillMasterID <= 0) {
					this.playError();
					this.BillDataMessage = '当前单据未提交，不能审核或消审！';
					return;
				}

				var aCommitRecallName = '审核';
				if (this.CommitType == ''){
					aCommitRecallName = '审核';
				} else {
					aCommitRecallName = '消审';
				}

				if (this.CommitProcName == ''){
					this.playError();
					this.BillDataMessage = '当前'+ aCommitRecallName +'操作配置有误，不能审核！';
					return;
				}

				uni.request({
					url: util.apiurl + 'rest/db/storedproc',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							proc_name: this.CommitProcName,
							method: 'open_proc',
							params: [{
								name: '@BillID',
								value: this.GoodsStationMoveBillID
							}, {
								name: '@BillNo',
								value: this.GoodsStationMoveBillNo
							}, {
								name: '@UserName',
								value: getApp().globalData.UserName
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0) {
							var aResultData = JSON.parse(res.data.data);
							if (aResultData.BillDataStatus == '0' && aResultData.BillDataMessage == 'SUCCESS') {
								this.playSuccess();
								this.BillDataMessage = aCommitRecallName + "成功！";

								if (aCommitRecallName == '审核'){
									this.CommitType = '已审核'
								} else {
									this.CommitType = ''
								}
							} else {
								this.playError();
								this.BillDataMessage = aCommitRecallName + '出错！' + aResultData.BillDataMessage;
								return;
							}
						} else {
							this.playError();
							this.BillDataMessage = aCommitRecallName + '出错，' + res.data.msg
							return;
						}
					},
					fail: (error) => {
						this.playError();
						this.BillDataMessage = '连接服务器出错，请检查后台服务是否启动！' + res.data.msg;
					},
				})
			},

		}
	}
</script>

<style>
	page {
		background-color: #F8F8F8;
		padding-bottom: 260rpx;
	}

	.u-radio {
		width: 200rpx !important;
	}

	.submitView {
		width: 100%;
		padding: 16rpx 0 26rpx;
		background-color: #FFFFFF;
		position: fixed;
		bottom: 0;
		left: 0;
		border-top: 1rpx solid #f1f1f1;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 100;
	}

	.submitBtn {
		width: 666rpx;
	}

	.productBox {
		background-color: #FFFFFF;
		margin-top: 32rpx;
		padding: 26rpx 0;
	}

	.tjcpName {
		width: 686rpx;
		height: 40rpx;
		font-size: 16px;
		font-weight: bold;
		border-left: 6rpx solid #007AFF;
		padding-left: 12rpx;
		margin-left: 26rpx;
		margin-top: 26rpx;
	}

	.cpInput {
		width: 150rpx !important;
		margin-right: 12rpx;
	}

	.cpInput>input {
		box-sizing: border-box;
		border: 1rpx solid #DDDDDD;
		width: 100%;
		height: 60rpx;
		border-radius: 10rpx;
		padding: 0 10rpx;
	}

	.cpInput1 {
		width: 200rpx !important;
		margin-right: 12rpx;
	}

	.cpInput1>input {
		box-sizing: border-box;
		border: 1rpx solid #DDDDDD;
		width: 100%;
		height: 60rpx;
		border-radius: 10rpx;
		padding: 0 10rpx;
	}

	.clearIcon {
		position: absolute;
		right: 6rpx;
		top: 6rpx;
	}

	.greenPrice {
		font-size: 16px;
		color: #19BE6B !important;
		font-weight: bold;
	}

	.disFlex {
		display: flex;
		align-items: center;
		margin-bottom: 8rpx;
	}

	.inputName {
		color: #ADADAD;
		font-size: 16px;
	}

	.addHKQS {
		display: flex;
		align-items: center;
		padding: 16rpx 26rpx;
		font-size: 15px;
		font-weight: bold;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>
