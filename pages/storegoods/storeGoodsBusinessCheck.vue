<template>
	<view>
		<button type="primary" @click="onShowDatePicker('range')">单据日期 {{range[0]}} 至 {{range[1]}}</button>
		<view class="u-demo-block">
			<view class="u-page__tag-item">
				<u-search v-model="searchBillNo" :show-action="false" @custom="onTabChange" @search="onTabChange"
					placeholder="请输入单号或往来单位">
				</u-search>
			</view>
		</view>

		<u-sticky>
			<div style="height: 43px; border-bottom: 1rpx solid #eeeeee; background-color: #FFFFFF;">
				<u-tabs :list="tabList" name="title" active-color="red" :is-scroll="false" :current="activeTabIndex"
					@change="onTabChange"></u-tabs>
			</div>
		</u-sticky>
		<dataNull v-if="list.length == 0" src="/static/img/chahua/gjNull.png" title="暂无相关出仓资料" title1="请添加或者更换搜索添加">
		</dataNull>
		<!-- 		<scroll-view v-else scroll-y="true" :style="{height: scrollHeight}" 
			refresher-enabled :refresher-threshold="200" :refresher-triggered="triggered" refresher-background="gray">
			<view v-for="(item, index) in ItemDataList" :key="index" @click="cardClickFun(item, index)">
				<GoodsBusinessCheckItem :item="item" :isSelect="isSelect" :index="index"></GoodsBusinessCheckItem>
			</view>
		</scroll-view> -->

		<scroll-view v-else scroll-y="true" :style="{height: scrollHeight}" @scrolltolower="selectDataFun"
			refresher-enabled :refresher-threshold="200" :refresher-triggered="triggered" refresher-background="gray"
			@refresherrefresh="onRefresh" @refresherrestore="onRestore">
			<view v-for="(item, index) in list" :key="index" @click="cardClickFun(item, index)">
				<GoodsBusinessCheckView :item="item" :isSelect="isSelect" :index="index"></GoodsBusinessCheckView>
			</view>
			<getMore :isMore="isMore"></getMore>
		</scroll-view>

		<addBtn url="./storeGoodsBusinessCheckAdd"></addBtn>
		<mx-date-picker :show="showPicker" :type="BillDatetype" :value="BillDatevalue" :show-tips="true"
			:begin-text="'开始'" :end-text="'结束'" :show-seconds="false" @confirm="BillDateConfirm"
			@cancel="BillDateConfirm" />

	</view>
</template>

<script>
	let that = '';
	import addBtn from '@/components/addBtn/addBtn.vue'
	import GoodsBusinessCheckView from '@/pages/storegoods/storeGoodsBusinessCheckView.vue';
	import util from '../../common/util';
	import MxDatePicker from "@/components/mx-datepicker/mx-datepicker.vue";
	export default {
		components: {
			GoodsBusinessCheckView,
			MxDatePicker
		},
		data() {
			return {
				activeTabIndex: 0,
				list: [],
				pageIndex: 1,
				isMore: true,
				scrollHeight: '667px',
				triggered: false,
				isSelect: false,
				searchBillNo: '',
				showPicker: false,
				BillBeginDate: this.$u.timeFormat(new Date(), 'yyyy-mm-dd'),
				BillEndDate: this.$u.timeFormat(new Date(), 'yyyy-mm-dd'),
				range: [this.$u.timeFormat(new Date(), 'yyyy-mm-dd'), this.$u.timeFormat(new Date(), 'yyyy-mm-dd')],
				BillDatetype: 'range',
				BillDatevalue: '',
				tabList: [{
					title: '未审核',
					status: '0'
				}, {
					title: '已审核',
					status: '1'
				}, ],
			}
		},
		onLoad(e) {
			that = this;
			uni.getSystemInfo({
				success(res) {
					that.scrollHeight = res.windowHeight - 40 + 'px';
				}
			})
			this.selectDataFun();
			//uni.$on('deleteCardFun', that.deleteCardFun);
			//uni.$on('updateBjdListByIndex', that.updateBjdListByIndex)
			//uni.$on('addBjdItemInListFun', that.addBjdItemInListFun)
		},
		watch: {
			activeTabIndex: {
				deep: true, // 深度监听
				handler(newVal, oldVal) {
					//console.log("activeTabIndex参数改变，即将刷新...", `新值：${newVal}`, `旧值：${oldVal}`);
					that.selectDataFun(newVal);
					//this.doRefresh(this.tbOrderCouponList)
				}
			}
		},
		// 上拉加载
		onReachBottom: function() {

		},
		onBackPress() {

		},
		methods: {
			onTabChange(index) {
				const _self = this;
				this.activeTabIndex = index;
			},

			onShowDatePicker(type) { //显示
				this.BillDatetype = type;
				this.showPicker = true;
				this.BillDatevalue = this[type];
			},

			BillDateConfirm(e) { //选择
				this.showPicker = false;
				if (e) {
					//this[this.BillDatetype] = e.value;
					this.BillBeginDate = this.$u.timeFormat(Date.parse(e.value[0]),'yyyy-mm-dd');
					this.BillEndDate = this.$u.timeFormat(Date.parse(e.value[1]),'yyyy-mm-dd');
					this.range = [this.BillBeginDate, this.BillEndDate];
					this.selectDataFun();
				}
			},

			/**
			 * 加载更多数据中
			 * @param infiniteScroll
			 */
			doInfinite(infiniteScroll) {
				const _self = this;
				new TbOrderCouponService().findTableTbOrderCouponList({
					pagination: _self.pagination,
					data: {
						communityId: _self.globalVariable.userInfo.tbCommunity.id,
						userId: _self.globalVariable.userInfo.id,
						status: _self.tabList[_self.activeTabIndex].status,
						orderBy: 'create_time desc',
					},
				}).then(result => {
					_self.pagination.refreshPage(result);
					_self.tbOrderCouponList = [..._self.tbOrderCouponList, ...result.list];
				});
			},


			selectDataFun: function() {
				if (that.activeTabIndex == 0)
				{
					util.request({
						url: '/product/productCheckOrder/getProductCheckOrderList',
						data: {
							'audit_status': '1',
							method: 'GET',
						},
						success: (res) => {
							this.triggered = false;
							that.list = [];
							let data = res.data.data.list;
							//console.log("--->" + JSON.stringify(res.data.data));
							if (res.data.data.total != 0){
								if (that.pageIndex == 1) {
									that.list = [];
								}
								if (res.data == 10) {
									that.pageIndex += 1;
									that.isMore = true;
								} else {
									that.isMore = false;
								}
								that.list = that.list.concat(data);
							}
						},
					})
				}
				else if (that.activeTabIndex == 1){
					util.request({
						url: '/product/productCheckOrder/getProductCheckOrderList',
						data: {
							'audit_status': '2',
							'start_check_time': this.BillBeginDate ,
							'end_check_time': this.BillEndDate,
							method: 'GET',
						},
						success: (res) => {
							console.log("-->>--" + JSON.stringify(res.data.data));
							this.triggered = false;
							that.list = [];
							let data = res.data.data.list;
							
							if (res.data.data.total != 0){
								if (that.pageIndex == 1) {
									that.list = [];
								}
								if (res.data == 10) {
									that.pageIndex += 1;
									that.isMore = true;
								} else {
									that.isMore = false;
								}
								that.list = that.list.concat(data);
							}
						},
					})
				}
			},
			// 卡片点击方法
			cardClickFun: function(item, index) {
				uni.navigateTo({
					url: '/pages/storegoods/storeGoodsBusinessCheckDetail?billid=' + item.id
				})
			},

			// 搜索框回调方法
			searchBoxEmitFun: function(e) {
				that.searchValue = e.searchValue1;
				that.cxGetDataFun()
			},
			// 下拉刷新
			onRefresh: function() {
				if (that.triggered) return
				that.triggered = true;
				that.cxGetDataFun();
			},
			onRestore: function(e) {
				that.triggered = false; // 需要重置
			},
			// 重新获取数据
			cxGetDataFun: function() {
				that.pageIndex = 1;
				that.isMore = true;
				that.selectDataFun();
			},
			deleteCardFun: function(e) {
				let arr = that.list;
				arr.splice(e.index, 1);
				that.list = arr;
			},
			// add页面新增数据
			addBjdItemInListFun: function(e) {
				that.cxGetDataFun()
			},
			// 通过下标更新list数据
			updateBjdListByIndex: function(e) {
				that.$set(that.list, parseInt(e.index), e.obj)
			},

		}
	}
</script>

<style>
	page {
		background-color: #F8F8F8;
	}

	button {
		margin: 20upx;
		font-size: 28upx;
	}
</style>
