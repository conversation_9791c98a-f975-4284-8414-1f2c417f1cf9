<template>
	<view class="wrap">
		<u-form ref="uForm">
			<u-form-item>
				<text class="title" style="width:200px;">单号：{{BillNo}}</text>
				<text class="title" style="width:200px;">日期：{{BillDate}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">往来单位：{{CustomerName}}</text>
				<text class="title" style="width:200px;">仓库名称：{{StoreName}}</text>
			</u-form-item>
			<u-form-item>

				<text class="title" style="width:80px;">仓位编号：</text>
				<input type="text" v-model="StoreStationNo" style="width:120px;" @confirm="GetStoreStationName" />
				<text class="title" style="width:150px;">仓位名称：{{StoreStationName}}</text>
			</u-form-item>
			<u-form-item label-width="150" label="条码资料:">
				<input type="text" v-model="QRBarCode" maxlength="-1" :focus="testFocus1" style="width:200px;"
					@confirm="DyeBackBillDetailScan" />
				<checkbox-group @change="BarCodeDelChange">
					<checkbox ref="checkBoxRef" :checked="BarCodeDelStatus">删除</checkbox>
				</checkbox-group>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">成品编号：{{FabricGoodsNo}}</text>
				<text class="title" style="width:200px;">成品缸号：{{CrockNo}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">成品色号：{{GoodsCodeNo}}</text>
				<text class="title" style="width:200px;">成品颜色：{{GoodsCodeName}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">本缸条数：{{CrockNoSumRoll}}条</text>
				<text class="title" style="width:200px;">本缸重量：{{CrockNoSumQty}}Kg</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">已扫条数：{{CrockNoScanRoll}}条</text>
				<text class="title" style="width:200px;">已扫重量：{{CrockNoScanQty}}Kg</text>
			</u-form-item>
			<u-form-item>
				<text class="title">{{BillDataMessage}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">本单总条：{{BillSumRoll}}</text>
				<text class="title" style="width:200px;">本单总重：{{BillSumQty}}</text>
			</u-form-item>
		</u-form>
		<view class="u-demo-area">
			<u-toast ref="uToast"></u-toast>
			<wyb-table ref="table" :headers="headersMaster" :contents="GoodsDetailList" />
			<!--	<wyb-table ref="table" :headers="headersDetail" :contents="GoodsCrockNoDataList"  height="600rpx" /> !-->
		</view>
		<!-- 		<u-action-sheet :list="InDetailList" v-model="actionSheetShow"  @click="actionSheetCallback"></u-action-sheet> -->

		<view class="submitView">
			<u-button type="primary" style="width:100px;" class="commitBtn"
				:ripple="true" ripple-bg-color="#909399" @click="commitBtnFun">
				{{CommitType ? '消审' : '审核'}}
			</u-button>
		</view>
	</view>
</template>

<script>
	import util, {
		parFabricGoodsBarCode2D,
		parYarnGoodsBarCode2D,
		playSuccessAudio,
		playErrorAudio
	} from '../../common/util';
	import wybTable from '@/components/wyb-table/wyb-table.vue';
	let that = '';
	export default {
		data() {
			return {
				BillMasterID: 0,
				borderColor: '#e4e7ed',
				align: 'center',
				index: 0,
				CommitType: '',
				CommitProcName: '',
				actionSheetShow: false,
				QRBarCode: '',
				BarCodeQtys: '',
				BillNo: '',
				BillDate: '',
				DyeWorksBillNo: '',
				DyeOrderDetailIDs: '',
				CustomerName: '',
				StoreNameID: 0,
				StoreName: '',
				StoreStationNo: '',
				StoreStationName: '',
				FabricGoodsNo: '',
				FabricGoodsName: '',
				GoodsCodeNo: '',
				GoodsCodeName: '',
				CrockNo: '',
				testFocus0: true,
				testFocus1: false,
				CrockNoSumRoll: 0,
				CrockNoSumQty: 0,
				CrockNoScanRoll: 0,
				CrockNoScanQty: 0,
				BillSumRoll: 0,
				BillSumQty: 0,
				BarCodeDelStatus: false,
				GoodsDetailList: [],
				BillDataMessage: '',
				headersMaster: [{
					label: '成品编号',
					key: 'FabricGoodsNo'
				}, {
					label: '成品色号',
					key: 'GoodsCodeNo'
				}, {
					label: '成品颜色',
					key: 'GoodsCodeName'
				}, {
					label: '成品缸号',
					key: 'CrockNo'
				}, {
					label: '成品条数',
					key: 'Roll'
				}, {
					label: '已扫条数',
					key: 'ScanRoll'
				}, {
					label: '已扫重量',
					key: 'ScanQty'
				}],
			}
		},

		onLoad(e) {
			that = this;
			if (e.billid) {
				that.BillMasterID = e.billid;
			}
			this.DyeWorksDyeBackDetail();
		},

		methods: {
			playSuccess() {
				util.playSuccessAudio();
			},
			playError() {
				util.playErrorAudio();
			},

			BarCodeDelChange: function() {
				this.BarCodeDelStatus = !this.BarCodeDelStatus;
			},

			GetStoreStationName: function() {
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetStoreStationName',
							params: [{
								name: 'No',
								value: this.StoreStationNo
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							this.playSuccess();
							var aResultData = res.data.data;
							for (var i = 0; i < aResultData.length; i++) {
								this.StoreStationName = aResultData[i].StoreStationName;
							};
							this.testFocus1 = false;
							this.$nextTick(() => {
								this.testFocus1 = true;
							});
						} else {
							this.playError();
							this.StoreStationName = "",
								this.testFocus0 = false;
							this.$nextTick(() => {
								//this.testFocus1 = false;
								this.testFocus0 = true;
							});
							this.StoreNameID = 0,
								this.BillDataMessage = '不存在当前的仓位资料，请确认！';
						}
					},
					fail: (error) => {
						this.playError();
						this.BillDataMessage = '连接服务器出错，请检查后台服务是否启动！' + res.data.msg;
					},
				})
			},

			DyeBackBillDetailScan() {
				this.testFocus0 = false;
				this.$nextTick(() => {
					this.testFocus0 = true;
				});

				let aBarCodeDelStatus = 0
				if (this.BarCodeDelStatus) {
					aBarCodeDelStatus = '1';
				};

				if (this.QRBarCode.indexOf(';') > 0) {
					var aBarCodeList = this.QRBarCode.split(';');
					if (aBarCodeList[0].indexOf('^') > 0) {
						var aBarCodeList = this.QRBarCode.split('^');
						this.DyeWorksBillNo = aBarCodeList[0];
						this.DyeOrderDetailIDs = aBarCodeList[1] + '^' + aBarCodeList[2] + '^' +
							aBarCodeList[4] + '^' + aBarCodeList[5] + '^' + aBarCodeList[6] + '^' +
							aBarCodeList[7] + '^';
						this.CrockNo = aBarCodeList[3];
						this.BarCodeQtys = aBarCodeList[8];
					} else {
						this.DyeWorksBillNo = aBarCodeList[0];
						this.DyeOrderDetailIDs = aBarCodeList[1];
						this.CrockNo = aBarCodeList[2];
						this.BarCodeQtys = aBarCodeList[4];

					}
				} else {
					this.DyeWorksBillNo = "";
					this.DyeOrderDetailIDs = "";
					this.CrockNo = "";
					this.BarCodeQtys = this.QRBarCode;
				}
				uni.request({
					url: util.apiurl + 'rest/db/storedproc',
					data: {
						token: getApp().globalData.Token,
						data: {
							db_name: getApp().globalData.AppDBName,
							proc_name: 'Usp_APP_DyeWorksDyeBackBillDetail',
							method: 'open_proc',
							params: [{
									name: '@BillMasterID',
									value: this.BillMasterID
								},
								{
									name: '@DyeOrderDetailIDs',
									value: this.DyeOrderDetailIDs
								},
								{
									name: '@DyeWorksBillNo',
									value: this.DyeWorksBillNo
								},
								{
									name: '@CrockNo',
									value: this.CrockNo
								},
								{
									name: '@StoreStationName',
									value: this.StoreStationNo
								},
								{
									name: '@BarCodeQtys',
									value: this.BarCodeQtys
								},
								{
									name: '@BarCodeDelStatus',
									value: aBarCodeDelStatus
								},
								{
									name: '@UserName',
									value: getApp().globalData.UserName
								}
							]
						},
					},
					success: (res) => {
						if (res.data.status == 0) {
							console.log(res.data.data.toString());
							var aResultData = JSON.parse(res.data.data);
							if (aResultData.BillDataStatus == '0' && aResultData.BillDataMessage ==
								'SUCCESS') {
								this.playSuccess();
								this.FabricGoodsNo = aResultData.FabricGoodsNo;
								this.FabricGoodsName = aResultData.FabricGoodsName;
								this.GoodsCodeNo = aResultData.GoodsCodeNo;
								this.GoodsCodeName = aResultData.GoodsCodeName;
								this.CrockNoSumRoll = parseFloat(aResultData.CrockNoSumRoll);
								this.CrockNoSumQty = parseFloat(aResultData.CrockNoSumQty);
								this.CrockNoScanRoll = parseFloat(aResultData.CrockNoScanRoll);
								this.CrockNoScanQty = parseFloat(aResultData.CrockNoScanQty);
								this.BillSumRoll = parseFloat(aResultData.BillSumRoll);
								this.BillSumQty = parseFloat(aResultData.BillSumQty);
								this.QRBarCode = '';
								this.BillDataMessage = '扫描成功！';
								this.DyeWorksDyeBackDetail();
								this.testFocus1 = false;
								this.$nextTick(() => {
									this.testFocus1 = true;
								});
							} else {
								this.playError();
								this.BillDataMessage = '出仓出错，' + aResultData.BillDataMessage;
								this.QRBarCode = '';
								this.testFocus1 = false;
								this.$nextTick(() => {
									this.testFocus1 = true;
								});
								return;
							}

						} else {
							this.playError();
							this.QRBarCode = '';
							this.BillDataMessage = '盘点扫描出错，' + res.data.msg;
							this.testFocus1 = false;
							this.$nextTick(() => {
								this.testFocus1 = true;
							});
							return;
						}
					},
					fail: (error) => {
						this.playError();
						this.QRBarCode = '';
						this.testFocus1 = false;
						this.$nextTick(() => {
							//this.testFocus0 = false;
							this.testFocus1 = true;
						});
						this.BillDataMessage = '入仓出错，' + res.data.msg;
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},

			DyeWorksDyeBackDetail: function() {
				if (this.BillMasterID == 0) {
					this.BillMasterID = 30685
				}
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetDyeWorksDyeBackDetailSQL',
							params: [{
								name: 'BillID',
								value: this.BillMasterID
							}]
						},
					},
					success: (res) => {
						this.triggered = false;
						let data = res.data.data;
						console.log("---->" + JSON.stringify(res.data));
						if (res.data.status == 0 && res.data.count > 0) {
							var aResultData = res.data.data;
							this.GoodsDetailList = res.data.data;
							this.BillNo = aResultData[0].BillNo;
							this.BillDate = aResultData[0].BillDate;
							this.CustomerName = aResultData[0].CustomerName;
							this.StoreNameID = aResultData[0].StoreNameID;
							this.StoreName = aResultData[0].StoreName;
							this.CommitType = aResultData[0].CommitType;
							this.CommitProcName = aResultData[0].CommitProcName;
							this.BillSumRoll = 0;
							this.BillSumQty = 0;
							for (var i = 0; i < aResultData.length; i++) {
								if (parseFloat(aResultData[i].Roll) > 0) {
									this.BillSumRoll = this.BillSumRoll + aResultData[i].Roll;
								}

								if (parseFloat(aResultData[i].Qty) > 0) {
									this.BillSumQty = this.BillSumQty + aResultData[i].Qty;
								}
							};
							this.BillSumRoll = this.BillSumRoll.toFixed(2) + '条';
							this.BillSumQty = this.BillSumQty.toFixed(2) + 'Kg';

						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.GoodsDetailList = [];
						} else {
							this.GoodsDetailList = [];
						}

					},
				})
			},

			// 审核按钮方法
			commitBtnFun: function() {
				if (this.BillMasterID <= 0) {
					this.playError();
					this.BillDataMessage = '当前单据未提交，不能审核或消审！';
					return;
				}

				var aCommitRecallName = '审核';
				if (this.CommitType == ''){
					aCommitRecallName = '审核';
				} else {
					aCommitRecallName = '消审';
				}

				if (this.CommitProcName == ''){
					this.playError();
					this.BillDataMessage = '当前'+ aCommitRecallName +'操作配置有误，不能审核！';
					return;
				}

				uni.request({
					url: util.apiurl + 'rest/db/storedproc',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							proc_name: this.CommitProcName,
							method: 'open_proc',
							params: [{
								name: '@BillID',
								value: this.BillMasterID
							}, {
								name: '@BillNo',
								value: this.BillNo
							}, {
								name: '@UserName',
								value: getApp().globalData.UserName
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0) {
							var aResultData = JSON.parse(res.data.data);
							if (aResultData.BillDataStatus == '0' && aResultData.BillDataMessage == 'SUCCESS') {
								this.playSuccess();
								this.BillDataMessage = aCommitRecallName + "成功！";

								if (aCommitRecallName == '审核'){
									this.CommitType = '已审核'
								} else {
									this.CommitType = ''
								}
							} else {
								this.playError();
								this.BillDataMessage = aCommitRecallName + '出错！' + aResultData.BillDataMessage;
								return;
							}
						} else {
							this.playError();
							this.BillDataMessage = aCommitRecallName + '出错，' + res.data.msg
							return;
						}
					},
					fail: (error) => {
						this.playError();
						this.BillDataMessage = '连接服务器出错，请检查后台服务是否启动！' + res.data.msg;
					},
				})
			},
		}
	}
</script>

<style>
	page {
		background-color: #F8F8F8;
		padding-bottom: 260rpx;
	}

	.u-radio {
		width: 200rpx !important;
	}

	.submitView {
		width: 100%;
		padding: 16rpx 0 26rpx;
		background-color: #FFFFFF;
		position: fixed;
		bottom: 0;
		left: 0;
		border-top: 1rpx solid #f1f1f1;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 100;
	}

	.submitBtn {
		width: 666rpx;
	}

	.productBox {
		background-color: #FFFFFF;
		margin-top: 32rpx;
		padding: 26rpx 0;
	}

	.tjcpName {
		width: 686rpx;
		height: 40rpx;
		font-size: 16px;
		font-weight: bold;
		border-left: 6rpx solid #007AFF;
		padding-left: 12rpx;
		margin-left: 26rpx;
		margin-top: 26rpx;
	}

	.cpInput {
		width: 150rpx !important;
		margin-right: 12rpx;
	}

	.cpInput>input {
		box-sizing: border-box;
		border: 1rpx solid #DDDDDD;
		width: 100%;
		height: 60rpx;
		border-radius: 10rpx;
		padding: 0 10rpx;
	}

	.cpInput1 {
		width: 200rpx !important;
		margin-right: 12rpx;
	}

	.cpInput1>input {
		box-sizing: border-box;
		border: 1rpx solid #DDDDDD;
		width: 100%;
		height: 60rpx;
		border-radius: 10rpx;
		padding: 0 10rpx;
	}

	.clearIcon {
		position: absolute;
		right: 6rpx;
		top: 6rpx;
	}

	.greenPrice {
		font-size: 16px;
		color: #19BE6B !important;
		font-weight: bold;
	}

	.disFlex {
		display: flex;
		align-items: center;
		margin-bottom: 8rpx;
	}

	.inputName {
		color: #ADADAD;
		font-size: 16px;
	}

	.addHKQS {
		display: flex;
		align-items: center;
		padding: 16rpx 26rpx;
		font-size: 15px;
		font-weight: bold;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>
