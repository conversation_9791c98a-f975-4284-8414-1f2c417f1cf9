<template>
	<view class="wrap">
		<u-form ref="uForm">
			<u-form-item label="仓库名称" label-width="150" prop="StoreName">
				<u-input :border="border" type="select" :select-open="actionSheetShow" v-model="StoreName"
					placeholder="请选择仓库" @click="actionSheetShow = true"></u-input>
				<button class="mini-btn" type="primary" size="mini" @tap="ClearStoreNameData">清空</button>
			</u-form-item>
			<u-form-item>
				<u-search shape="round" v-model="FabricGoodsName" placeholder="请输入坯布编号" actionText=""></u-search>
			</u-form-item>
			<u-form-item>
				<u-search shape="round" v-model="GoodsCodeName" placeholder="请输入色号颜色" actionText=""></u-search>
				<button class="mini-btn" type="primary" size="mini" @tap="GetDetailListData">查询</button>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">总条数：{{GoodsSumRoll}}</text>
				<text class="title" style="width:200px;">总重量：{{GoodsSumQty}}</text>
			</u-form-item>
		</u-form>
		<view class="u-demo-area">
			<u-toast ref="uToast"></u-toast>
			<wyb-table ref="table" :headers="headersMaster" :contents="FabricGoodsDataList" @onCellClick="GetDetailCrockNoListData"	/>
		<!--	<wyb-table ref="table" :headers="headersDetail" :contents="GoodsCrockNoDataList"  height="600rpx" /> !-->
		</view>
		<u-action-sheet :list="StoreNameDataList" v-model="actionSheetShow"  @click="actionSheetCallback"></u-action-sheet>
	</view>
</template>

<script>
	import util, {parFabricGoodsBarCode2D} from '../../common/util';
	import wybTable from '@/components/wyb-table/wyb-table.vue';
    export default {
        name: "btable",
        data() {
            return {
				borderColor: '#e4e7ed',
				align: 'center',
				index: 0,
				actionSheetShow: false,
				StoreName:'',
				FabricGoodsName:'',
				GoodsCodeName:'',
				GoodsSumRoll:0,
				GoodsSumQty:0,
                FabricGoodsDataList:[],
				GoodsCrockNoDataList:[],
				StoreNameDataList: [],
				headersMaster: [{
				                label: '编号',
				                key: 'FabricGoodsNo'
				            },{
				                label: '名称',
				                key: 'FabricGoodsName'
				            }, {
				                label: '色号',
				                key: 'GoodsCodeNo'
				            }, {
				                label: '颜色',
				                key: 'GoodsCodeName'
				            }, {
				                label: '条数',
				                key: 'Roll'
				            }, {
				                label: '数量',
				                key: 'Qty'
				            }, {
				                label: '仓库',
				                key: 'StoreName'
				            }],
				headersDetail: [ {
				                label: '编号',
				                key: 'FabricGoodsNo'
				            },{
				                label: '名称',
				                key: 'FabricGoodsName'
				            }, {
				                label: '色号',
				                key: 'GoodsCodeNo'
				            }, {
				                label: '颜色',
				                key: 'GoodsCodeName'
				            }, {
				                label: '缸号',
				                key: 'CrockNo'
				            }, {
				                label: '条数',
				                key: 'Roll'
				            }, {
				                label: '数量',
				                key: 'Qty'
				            }]

            }
        },
		onLoad() {
			this.getStoreNameData();
		},
        methods: {
            upx2px(value) {
                //#ifndef MP-WEIXIN
                return uni.upx2px(value) + 'px'
                //#endif
                //#ifdef MP-WEIXIN
                return uni.upx2px(value)
                //#endif
            },
            rowClick(e) {
                console.log(e)
            },
            pullup(){
                console.log('上拉')
            },
			// 点击actionSheet回调
			actionSheetCallback(index) {
				uni.hideKeyboard();
				this.StoreName = this.StoreNameDataList[index].text;
			},

			ClearStoreNameData() {
				this.StoreName = '';
			},

			getStoreNameData() {
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetStoreNameList',
							params: [{
								name: 'TypeNo',
								value: '%StoreFabricGoods%'
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							var aResultData = res.data.data;
							this.StoreNameDataList = res.data.data;
						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.StoreNameDataList = [];
						} else {
							this.StoreNameDataList = [];
						}
					},
					fail: (error) => {
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},

			GetDetailListData: function() {
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetStoreGoodsMasterSQL',
							params: [{
								name: 'StoreName',
								value: '%'+this.StoreName+'%'
							},{
								name: 'FGNo',
								value: '%'+this.FabricGoodsName+'%'
							},{
								name: 'FGName',
								value: '%'+this.FabricGoodsName+'%'
							},{
								name: 'GCNO',
								value: '%'+this.GoodsCodeName+'%'
							},{
								name: 'GCName',
								value: '%'+this.GoodsCodeName+'%'
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							this.FabricGoodsDataList = res.data.data;
							var aResultData = res.data.data;
							this.GoodsSumRoll = 0;
							this.GoodsSumQty = 0;
							for (var i = 0; i < aResultData.length; i++) {
								this.GoodsSumRoll = this.GoodsSumRoll + aResultData[i].Roll;
								this.GoodsSumQty = this.GoodsSumQty + aResultData[i].Qty;
							};
							this.GoodsSumRoll = this.GoodsSumRoll.toFixed(2) + '条';
							this.GoodsSumQty = this.GoodsSumQty.toFixed(2) + 'Kg';
						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.FabricGoodsDataList = [];
						} else {
							this.FabricGoodsDataList = [];
						}
					},
					fail: (error) => {
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},
        },

		GetDetailCrockNoListData: function(index) {
			console.log(this.FabricGoodsDataList[index].FabricGoodsName);
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP_GetStoreGoodsDetailCrockNoSQL',
							params: [{
								name: 'StoreName',
								value: this.StoreName
							},{
								name: 'FGNo',
								value: this.FabricGoodsName
							},{
								name: 'GCNO',
								value: this.GoodsCodeName
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							var aResultData = res.data.data;
							this.GoodsCrockNoDataList = [];
							for (var i = 0; i < aResultData.length; i++) {
								this.GoodsCrockNoDataList.push(aResultData[i]);
							};
						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.GoodsCrockNoDataList = [];
						} else {
							this.GoodsCrockNoDataList = [];
						}
					},
					fail: (error) => {
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},
    }
</script>

<style lang="scss" scoped>
    .uni-progress {
        color: red;

        ::v-deep( .uni-progress-info) {
            font-size: 10px !important;
        }
    }
</style>
